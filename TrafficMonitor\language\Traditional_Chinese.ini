﻿[general]
BCP_47 = "zh-TW"
DISPLAY_NAME = "繁體中文"
TRANSLATOR = "GT Wang"
TRANSLATOR_URL = "http://mkvq.blogspot.com/"
DEFAULT_FONT = "Microsoft JhengHei"

[text]
; String Table
IDS_CHECK_UPDATE_FAILD = "檢查更新失敗，請檢查你的網路連線！"
IDS_CHECK_UPDATE_ERROR = "檢查更新失敗，從遠端更新檔得到了錯誤的資訊，請聯絡作者！"
IDS_UPDATE_AVLIABLE = "檢測到新版本 V%s，是否前往更新？"
IDS_UPDATE_AVLIABLE2 = "檢測到新版本 V%s，更新內容：\r\n%s\r\n是否前往更新？"
IDS_ALREADY_UPDATED = "目前已是最新版本。"
IDS_AUTORUN_FAILED_NO_KEY= "無法實現開機自行啟動，在登錄檔中找不到相應的鍵值"
IDS_AUTORUN_FAILED_NO_ACCESS= "登錄機碼寫入失敗，可能該鍵值沒有權限存取"
IDS_AUTORUN_DELETE_FAILED= "登錄機碼刪除失敗，可能該鍵值沒有權限存取"
IDS_AN_INSTANCE_RUNNING= "已有一個程式正在執行。"
IDS_TRAFFIC_USED_TODAY = "今日已使用流量"
IDS_MEMORY_USAGE = "記憶體使用量"
IDS_CPU_USAGE = "CPU使用量"
IDS_SEND_EMAIL_TO_ATHOUR= "傳送電子郵件給作者"
IDS_GOTO_GITHUB = "前往此專案在 GitHub 上的頁面"
IDS_DONATE_ATHOUR = "贊助作者"
IDS_UPLOAD = "上傳"
IDS_DOWNLOAD = "下載"
IDS_MEMORY = "記憶體"
IDS_UPLOAD_DISP = "上傳"
IDS_DOWNLOAD_DISP = "下載"
IDS_MEMORY_DISP = "記憶體"
IDS_CONNOT_SAVE_CONFIG_WARNING = "警告：無法儲存設定，無法向檔案「<%file_path%>」寫入資料！以管理員身份執行程式可能會解決問題。"
IDS_TRAFFICMONITOR = "流量監視器"
IDS_INSUFFICIENT_BUFFER= "用於儲存連線資訊的緩衝區大小不夠，已重新初始化連線。(已重新初始化<%cnt%>次)"
IDS_CONNECTION_NUM_CHANGED = "檢測到連線數發生變化，已重新取得連線。先前連線數：<%before%>，現在連線數：<%after%>。(已重新初始化<%cnt%>次)"
IDS_CONNECTION_NOT_MATCH = "可能出現了異常，目前選擇的連線和期望的連線不一致，已重新取得連線。(已重新初始化<%cnt%>次)"
IDS_CONNOT_INSERT_TO_TASKBAR = "視窗沒有成功嵌入工作列，可能已被防毒軟體阻止或者開始功能表未關閉，請嘗試重啟 explorer。TrafficMonitor 會繼續嘗試但不再提醒，錯誤代碼："
IDS_MEMORY_UDAGE_EXCEED= "記憶體使用率已達到"
IDS_NOTIFY = "通知"
IDS_TODAY_TRAFFIC_EXCEED= "今日已使用流量已達到"
IDS_DATE = "日期"
IDS_TRAFFIC_USED = "總流量"
IDS_FIGURE = "圖表"
IDS_DEFAULT_ICON = "預設圖示"
IDS_ICON = "圖示"
IDS_INTERFACE_NAME = "介面名稱"
IDS_INTERFACE_DESCRIPTION= "介面描述"
IDS_CONNECTION_TYPE = "連線類型"
IDS_IF_TYPE_OTHER = "其他類型的網路"
IDS_IF_TYPE_ETHERNET_CSMACD= "乙太網路"
IDS_IF_TYPE_ISO88025_TOKENRING= "Token Ring 網路"
IDS_IF_TYPE_FDDI = "光纖分散式資料介面 (FDDI) 網路"
IDS_IF_TYPE_PPP = "PPP 網路"
IDS_IF_TYPE_SOFTWARE_LOOPBACK= "軟體 LoopBack 網路"
IDS_IF_TYPE_ATM = "ATM 網路"
IDS_IF_TYPE_IEEE80211 = "IEEE 802.11 無線網路"
IDS_IF_TYPE_TUNNEL = "隧道式封裝網路"
IDS_IF_TYPE_IEEE1394 = "IEEE 1394 (Firewire) 高效能串列匯流排網路"
IDS_IF_TYPE_IEEE80216_WMAN= "對於 WiMax 設備的行動寬頻介面"
IDS_IF_TYPE_WWANPP = "基於 GSM 設備的行動寬頻介面"
IDS_IF_TYPE_WWANPP2 = "基於 CDMA 設備的行動寬頻介面"
IDS_UNKNOW_CONNECTION = "未知網路"
IDS_SPEED = "速度"
IDS_ADAPTER_PHYSICAL_ADDRESS= "實體位址 (MAC)"
IDS_IP_ADDRESS = "IP 位址"
IDS_SUBNET_MASK = "子網路遮罩"
IDS_DEFAULT_GATEWAY = "預設閘道"
IDS_OPERATIONAL_STATUS = "連線狀態"
IDS_IF_OPER_STATUS_NON_OPERATIONAL= "LAN 介面卡已停用"
IDS_IF_OPER_STATUS_UNREACHABLE= "WAN 介面卡未連線"
IDS_IF_OPER_STATUS_DISCONNECTED= "網路連線中斷或無載體"
IDS_IF_OPER_STATUS_CONNECTING= "WAN 介面卡正在連線"
IDS_IF_OPER_STATUS_CONNECTED= "WAN 介面卡連線到遠端對等方"
IDS_IF_OPER_STATUS_OPERATIONAL= "LAN 介面卡已連線"
IDS_UNKNOW_STATUS = "未知狀態"
IDS_BYTES_RECEIVED = "已接收位元組數"
IDS_BYTES_SENT = "已傳送的位元組數"
IDS_BYTES_RECEIVED_SINCE_START= "自程式啟動以來已接收位元組數"
IDS_BYTES_SENT_SINCE_START= "自程式啟動以來已傳送的位元組數"
IDS_PROGRAM_ELAPSED_TIME= "程式已執行時間"
IDS_HOUR_MINUTE_SECOND = "%d小時%d分%d秒"
IDS_INTERNET_IP_ADDRESS= "外網 IP 位址"
IDS_GET_FAILED = "取得失敗"
IDS_ITEM = "項目"
IDS_VALUE = "值"
IDS_COPY_TO_CLIPBOARD_FAILED= "複製到剪貼簿失敗！"
IDS_SKIN_AUTHOUR = "面板作者："
IDS_SPEED_SHORT_MODE_TIP= "勾選後，網速顯示的小數點位數將減少1位，並且單位不顯示「B」"
IDS_AUTO = "自動"
IDS_FIXED_AS = "固定為"
IDS_OPEN_CONNECTION_DETIAL= "開啟連線詳細資料"
IDS_OPEN_HISTORICAL_TRAFFIC= "開啟歷史流量統計"
IDS_SHOW_HIDE_MORE_INFO= "顯示/隱藏更多資訊"
IDS_SHOW_HIDE_CPU_MEMORY= "顯示/隱藏 CPU 和記憶體使用量"
IDS_OPEN_OPTION_SETTINGS= "開啟設定選項"
IDS_OPEN_TASK_MANAGER = "開啟工作管理員"
IDS_CHANGE_SKIN = "更換面板"
IDS_NONE = "無"
IDS_FONT_SIZE_WARNING = "字型大小必須在 %d~%d 之間！"
IDS_SAME_TEXT_BACK_COLOR_WARNING= "警告：文字色彩和背景色相同！"
IDS_SAME_BACK_TEXT_COLOR_WARNING= "警告：背景色和文字色彩相同！"
IDS_FOLLOWING_SYSTEM = "跟隨系統"
IDS_LANGUAGE_CHANGE_INFO= "請重新啟動程式使語言設定生效。"
IDS_MAIN_WINDOW_SETTINGS= "主視窗設定"
IDS_TASKBAR_WINDOW_SETTINGS= "工作列視窗設定"
IDS_GENERAL_SETTINGS = "一般設定"
IDS_ACQUIRING = "正在取得"
IDS_LIST_VIEW = "清單檢視"
IDS_CALENDAR_VIEW = "日曆檢視"
IDS_MONDAY = "一"
IDS_TUESDAY = "二"
IDS_WEDNESDAY = "三"
IDS_THURSDAY = "四"
IDS_FRIDAY = "五"
IDS_SATURDAY = "六"
IDS_SUNDAY = "日"
IDS_CURRENT_MONTH_TOTAL_TRAFFIC= "目前月總流量:"
IDS_TRAFFIC_USED1 = "已使用流量:"
IDS_CONNOT_INSERT_TO_TASKBAR_ERROR_LOG = "嵌入工作列失敗，已重試 <%cnt%> 次，GetLastError(): <%error_code%>。"
IDS_NO_CONNECTION = "無連線"
IDS_CONTACT_TRANSLATOR = "聯絡此譯者"
IDS_THANKS_DONORS = "感謝以下贊助者："
IDS_GET_URL_ERROR_LOG_INFO= "請求「<%1%>」時出現了錯誤，錯誤程式碼：<%2%>。"
IDS_SHOW_ALL_INFO_TIP = "開啟此項後，右鍵選單中的「選擇網路連線」清單中會顯示出所有的網路介面。建議僅在需要的時候開啟此項。"
IDS_CFG_DIR_CHANGED_INFO = "您修改了設定檔和資料檔的儲存位置，重新啟動後生效。你可能需要手動將設定檔和資料檔轉移到新的位置。"
IDS_DOUBLE_CLICK_TO_ACQUIRE= "<按兩下此處取得>"
IDS_ERROR1 = "錯誤"
IDS_ERROR_MESSAGE = "錯誤資訊："
IDS_CRASH_INFO = "很抱歉，程式出現了錯誤，請重新啟動程式。\r\n如果你多次遇到這個問題，請嘗試到“選項”>“常規設定”中禁用硬體監控功能，並且到“外掛管理”對話方塊中禁用所有外掛。\r\n如果問題仍然存在，請將此檔案「<%1%>」透過電子郵件寄送到 <EMAIL>，以幫助作者確定問題所在。並將以下內容加入到信件內文中："
IDS_TITLE_ACKNOWLEDGEMENT= "鳴謝"
IDS_SAVE_DEFAULT_STYLE_INQUIRY= "確定要把目前工作列色彩設定儲存到「預設 <%1%> 」嗎？"
IDS_SPECIFIC_APP = "開啟指定應用程式"
IDS_EXE_FILTER = "應用程式|*.exe|批處理檔案|*.bat||"
IDS_PRESET = "預設"
IDS_LIGHT_MODE = "淺色模式"
IDS_AUTO_ADAPT_TIP_INFO= "此功能可以在 Windows10 深色/淺色主題改變時自動切換色彩預設方案，點選「自動適應設定」按鈕以配置用於自動切換的預設方案。"
IDS_WITHOUT_TEMPERATURE= "Lite"
IDS_MOUSE_PENETRATE_TIP_INFO = "已開啟滑鼠穿透，如果需要關閉滑鼠穿透，請在右下角系統通知區域找到 TrafficMonitor 的圖示，點選滑鼠右鍵，在選單中關閉滑鼠穿透。點選「取消」不再提示。"
IDS_HISTORY_TRAFFIC_LOST_ERROR_LOG = "偵測到歷史流量資料遺失，目前記錄個數：<%1%>，已從備份檔案恢復<%2%>條記錄。"
IDS_LEGEND = "圖例"
IDS_LICENSE_EXPLAIN = "使用本軟體的原始程式碼時請遵守以下開源協定。"
IDS_LICENSE = "開源協定"
IDS_DAY_VIEW = "日檢視"
IDS_MONTH_VIEW = "月檢視"
IDS_QUARTER_VIEW = "季檢視"
IDS_YEAR_VIEW = "年檢視"
IDS_LINEAR_SCALE = "線性比例"
IDS_LOG_SCALE = "對數比例"
IDS_CPU_TEMPERATURE = "CPU 溫度"
IDS_GPU_TEMPERATURE = "顯示卡溫度"
IDS_CPU_FREQ = "CPU頻率"
IDS_HDD_TEMPERATURE = "硬碟溫度"
IDS_MAINBOARD_TEMPERATURE= "主機板溫度"
IDS_GPU_DISP = "顯示卡"
IDS_HDD_DISP = "硬碟"
IDS_MAINBOARD_DISP = "主機板"
IDS_CPU_TEMPERATURE_EXCEED= "CPU 溫度已達到"
IDS_GPU_TEMPERATURE_EXCEED= "顯示卡溫度已達到"
IDS_HDD_TEMPERATURE_EXCEED= "硬碟溫度已達到"
IDS_MBD_TEMPERATURE_EXCEED= "主機板溫度已達到"
IDS_MUSICPLAYER2_DESCRIPTION= "MusicPlayer2: 美觀易用的 Windows 本機音樂播放器"
IDS_SIMPLENOTEPAD_DESCRIPTION= "SimpleNotePad: 簡潔的 Windows 文字編輯器"
IDS_COLOR = "色彩"
IDS_COLOR_LABEL = "標籤色彩"
IDS_COLOR_VALUE = "數值色彩"
IDS_GPU_USAGE = "顯示卡使用量"
IDS_IF_OPER_STATUS_UP = "介面卡已連線"
IDS_IF_OPER_STATUS_DOWN= "介面卡未連線"
IDS_IF_OPER_STATUS_DORMANT= "介面卡正在連線"
IDS_GOTO_GITEE = "前往此專案在 Gitee 上的頁面"
IDS_USAGE_PERCENTAGE = "已使用百分比"
IDS_MEMORY_USED = "記憶體已使用"
IDS_MEMORY_AVAILABLE = "記憶體可用"
IDS_DOTNET_NOT_INSTALLED_TIP = "偵測到系統中未安裝 .Net Framework 4.5.2 或更高版本，溫度監控功能將不可用。點選取消不再提示。"
IDS_VERSION_UPDATE = "版本更新"
IDS_AVREAGE_TEMPERATURE= "平均溫度"
IDS_HARDWARE_MONITOR_WARNING = "警告：你正在開啟硬體監控功能，硬體監控功能可以用於顯示溫度和顯示卡利用率資訊。在開啟硬體監控功能前請仔細閱讀以下事項：\r\nTrafficMonitor 不是專業的硬體監控軟體，它不能保證在任何一臺電腦上都能夠取得到硬體資訊，也無法保證取得到的硬體資訊的準確性。\r\n硬體監控功能是透過第三方程式庫 LibreHardwareMonitor 實現的，開啟硬體監控後在某些電腦中可能會出現一些問題，包括但不限於：\r\n* CPU 和記憶體佔用異常\r\n* 程式當掉\r\n* 電腦當機\r\n請在知曉以上風險再決定開啟硬體監控功能。\r\n確定要開啟硬體監控功能嗎？"
IDS_HDD_USAGE = "硬碟使用量"
IDS_FILE_NAME = "檔名"
IDS_STATUS = "狀態"
IDS_PLUGIN_LOAD_SUCCEED= "載入成功"
IDS_PLUGIN_MODULE_LOAD_FAILED= "外掛模組載入失敗，故障程式碼：<%1%>"
IDS_PLUGIN_FUNCTION_GET_FAILED= "函式獲取失敗，故障程式碼：<%1%>"
IDS_PLUGIN_INFO = "外掛詳細資訊"
IDS_NAME = "名稱"
IDS_DESCRIPTION = "描述"
IDS_FILE_PATH = "檔案路徑"
IDS_ITEM_NUM = "顯示專案個數"
IDS_ITEM_NAMES = "顯示專案名稱"
IDS_AUTHOR = "作者"
IDS_COPYRIGHT = "版權"
IDS_PLUGIN_NO_OPTIONS_INFO= "該外掛沒有提供選項設定。"
IDS_PLUGIN_NAME = "外掛名稱"
IDS_DISABLED = "已停用"
IDS_RESTART_TO_APPLY_CHANGE_INFO= "請重新啟動程式以套用此更改。"
IDS_VERSION = "版本"
IDS_DISP_ITEM_ID = "顯示專案 ID"
IDS_PLUGIN_API_VERSION = "介面版本"
IDS_WEEK_VIEW = "週檢視"
IDS_WEEK_NUM = "第 <%1%> 週"
IDS_URL = "連結"
IDS_PLUGIN_VERSION_NOT_SUPPORT= "外掛版本過低"
IDS_MODIFY_PRESET = "修改預設方案"
IDS_SELECT_AT_LEASE_ONE_WARNING= "請至少選擇一項！"
IDS_AUTO_SAVE_TO_PRESET_TIP = "在開啟了｢自動適應Windows 10 深色/淺色主題｣功能時，如果勾選此項，則會在工作列視窗色彩、背景色彩等設定更改時，根據目前 Windows 的深色/淺色主題，自動儲存到對應的預設中。"
IDS_TOTAL_NET_SPEED = "總網速"
IDS_SHOW_RESOURCE_USAGE_GRAPH_TIP= "在CPU/記憶體硬碟利用率、溫度資訊、以及外掛條目上顯示資源佔用圖"
IDS_SHOW_NET_SPEED_GRAPH_TIP= "在上傳、下載和總網速上顯示網速指示"
IDS_REFRESH_CONNECTION_LIST= "重新整理網路連線清單"
IDS_HARDWARE_MONITOR_INIT_FAILED= "硬體監控功能初始化失敗，硬體監控將無法使用！"
IDS_HARDWARE_INFO_ACQUIRE_FAILED_ERROR= "取得硬體監控資料時出現了錯誤！"
IDS_AUTO_RUN_METHOD_REGESTRY= "開機自動執行方式：登錄檔"
IDS_AUTO_RUN_METHOD_TASK_SCHEDULE= "開機自動執行方式：工作排程"
IDS_PATH = "路徑"
IDS_SET_AUTO_RUN_FAILED_WARNING= "設定開機自動執行失敗！"
IDS_UPDATE_TASKBARDLG_FAILED_TIP = "更新工作列視窗內容失敗，已禁用Direct2D渲染。 注意：設定視窗可能不會立刻更新這一調整。"
IDS_D2DDRAWCOMMON_ERROR_TIP = "Direct2D渲染過程中出現錯誤，已禁用Direct2D渲染。 注意：設定視窗可能不會立刻更新這一調整。"
IDS_PLUGIN_OPTIONS = "外掛選項"
IDS_GET_CPU_USAGE_BY_PDH_FAILED_LOG = "使用效能計數器獲取CPU利用率失敗，fullCounterPath=<%1%>"
IDS_PRIMARY_DISPLAY = "主顯示器"
IDS_SECONDARY_DISPLAY = "副顯示器 <%1%>"
IDS_RESTORE_FROM_SLEEP_LOG = "系統已從休眠狀態恢復，已重新初始化連線。（已重新初始化<%1%>次）"
IDS_PLUGIN_NEW_VERSION_INFO = "有更新，最新版本：<%1%>"
IDS_TRAFFICMONITOR_PLUGIN_NITIFICATION = "TrafficMonitor 外掛通知"

TXT_OK = "確定"
TXT_CANCEL = "取消"
TXT_CLOSE = "關閉"
TXT_APPLY = "套用"

; Text used for dialog. (Must be started with "TXT_")
; CAboutDlg
TXT_TITLE_ABOUT = "關於 TrafficMonitor"
TXT_ABOUT_VERSION = "TrafficMonitor<%1%>，<%2%> 版"
TXT_ABOUT_COPYRIGHT = "Copyright (C) 2017-2025 By ZhongYang\n最後編譯日期:  <compile_date>"
TXT_ABOUT_TRANSLATOR = "<%1%>翻譯：<%2%>"
TXT_ABOUT_THIRD_PARTY_LIB = "此項目中使用的第三方程式庫："
TXT_ABOUT_AUTHOR_S_OTHER_SOFTWARE = "作者的其他軟體："
TXT_ABOUT_CONTACT_AUTHOR = "聯絡作者"
TXT_ABOUT_LICENSE = "開源協定"
TXT_ABOUT_ACKNOWLEDGEMENT = "鳴謝"
TXT_ABOUT_DONATE = "贊助"

; CAppAlreadyRuningDlg
TXT_TRAFFICMONITOR_ALREAD_RUNING = "TrafficMonitor 已經在執行。"
TXT_EXIT_THE_PROGRAM = "結束程式(&X)"
TXT_OPEN_OPTION_SETTINGS = "開啟選項設定(&O)"
TXT_SHOW_HIDE_MAIN_WINDOW = "顯示/隱藏主視窗(&M)"
TXT_SHOW_HIDE_TASKBAR_WINDOW = "顯示/隱藏工作列視窗(&T)"

; CAutoAdaptSettingsDlg
TXT_TITLE_AUTO_ADATP_SETTINGS = "自动适应设置"
TXT_COLOR_PRESET_IN_DARK_MODE = "深色 Windows 模式下使用的色彩預設："
TXT_COLOR_PRESET_IN_LIGHT_MODE = "淺色 Windows 模式下使用的色彩預設："
TXT_AUTO_SAVE_TO_PRESET_CHECK = "自動儲存工作列視窗色彩設定到預設"

; CDisplayTextSettingDlg
TXT_TITLE_DISPLAY_TEXT_SETTING = "顯示文字設定"
TXT_RESTORE_DEFAULT = "全部還原預設(&D)"

; CDonateDlg
TXT_TITLE_DONATE = "贊助"
TXT_DONATE_INFO = "如果你覺得這個軟體對你有幫助，你可以掃描下面的二維條碼透過支付寶或微信贊助作者，以幫助作者把這個軟體做得更好。金額請隨意。"

; CGeneralSettingsDlg
TXT_APPLICATION_SETTINGS = "應用程式設定"
TXT_CHECK_UPDATE = "啟動時檢查更新"
TXT_CHECK_NOW = "立即檢查(&C)"
TXT_UPDATE_SOURCE = "更新來源："
TXT_AUTO_RUN_CHECK = "開機時自動執行"
TXT_RESET_AUTO_RUN_BUTTON = "重新設定開機自動執行"
TXT_LANGUAGE = "語言:"
TXT_CONFIGURATION_AND_DATA_FILES = "設定檔和資料檔"
TXT_SAVE_TO_APPDATA_RADIO = "儲存到 Appdata 目錄"
TXT_SAVE_TO_PROGRAM_DIR_RADIO = "儲存到程式所在目錄"
TXT_OPEN_CONFIG_PATH_BUTTON = "開啟設定檔所在目錄(&D)"
TXT_NOTIFICATION_MESSAGE = "通知訊息"
TXT_TODAY_TRAFFIC_TIP_CHECK = "今日已使用流量達到"
TXT_TODAY_TRAFFIC_BACK = "時通知"
TXT_MEMORY_USAGE_TIP_CHECK = "記憶體使用量達到"
TXT_MEMORY_USAGE_BACK = "% 時通知"
TXT_CPU_TEMP_TIP_CHECK = "CPU 溫度達到"
TXT_CPU_TEMP_BACK = "°C 時通知"
TXT_GPU_TEMP_TIP_CHECK = "顯示卡溫度達到"
TXT_GPU_TEMP_BACK = "°C 時通知"
TXT_HDD_TEMP_TIP_CHECK = "硬碟溫度達到"
TXT_HDD_TEMP_BACK = "°C 時通知"
TXT_MBD_TEMP_TIP_CHECK = "主機板溫度達到"
TXT_MBD_TEMP_BACK = "°C 時通知"
TXT_HARDWARE_MONITORING = "硬體監控"
TXT_CPU = "CPU"
TXT_GPU = "顯示卡"
TXT_HARD_DISK = "硬碟"
TXT_MAIN_BOARD = "主機板"
TXT_SELECT_HDD_STATIC = "選擇監控的硬碟："
TXT_SELECT_CPU_STATIC = "選擇監控的 CPU 溫度："
TXT_ADVANCED = "進階"
TXT_SHOW_ALL_CONNECTION_CHECK = "顯示所有網路連線"
TXT_SELECT_CONNECTIONS_BUTTON = "選擇要監控的網路連線(&S)..."
TXT_CPU_ACQUISITION_METHOD = "CPU 使用量取得方式："
TXT_USE_CPU_TIME_RADIO = "基於 CPU 使用時間"
TXT_USE_PDH_RADIO = "使用效能計數器"
TXT_USE_HARDWARE_MONITOR_RADIO = "使用硬體監控"
TXT_MONITORING_INTERVALS = "監控時間間隔："
TXT_MILLISECONDS = "毫秒"
TXT_RESTORE_DEFAULT_TIME_SPAN_BUTTON = "還原預設(&D)"
TXT_PLUGIN_MANAGE_BUTTON = "外掛管理(&P)..."
TXT_DISPLAY = "顯示"
TXT_SHOW_NOTIFY_ICON_CHECK = "顯示通知區域圖示"

; CHistoryTrafficCalendarDlg
TXT_YEAR = "年："
TXT_MONTH = "月："

; CHistoryTrafficDlg
TXT_TITLE_HISTORY_TRAFFIC = "歷史流量統計"

; CHistoryTrafficListDlg
TXT_VIEW_TYPE = "統計方式："
TXT_FIGURE_VIEW_SCALE = "圖表顯示比例："

; CIconSelectDlg
TXT_TITLE_CHANGE_ICON = "更換通知區域圖示"
TXT_SELECT_A_ICON = "選擇圖示："
TXT_PREVIEW = "預覽"
TXT_NOTIFY_ICON_AUTO_ADAPT_CHECK = "根據 Windows 10 深淺色模式自動適應"

; CMainWndColorDlg
TXT_TITLE_MAIN_COLOR_DIALOG = "主視窗色彩設定"

; CMainWndSettingsDlg
TXT_FULLSCREEN_HIDE_CHECK = "當程式全螢幕執行時隱藏主視窗"
TXT_COLOR_AND_FONT = "色彩和字型"
TXT_FONT = "字型："
TXT_FONT_SIZE = "字型大小："
TXT_SET_FONT_BUTTON = "選擇字型(&F)..."
TXT_TEXT_COLOR = "文字色彩："
TXT_RESOTRE_SKIN_DEFAULT_BUTTON = "恢復面板預設(&R)"
TXT_DISPLAY_TEXT = "顯示文字"
TXT_SWITCH_UP_DOWN_CHECK = "交換上傳和下載的位置"
TXT_UNIT_SETTINGS = "單位設定"
TXT_UNIT_SELECTION = "單位選擇："
TXT_HIDE_UNIT_CHECK = "不顯示網速單位"
TXT_SPEED_SHORT_MODE_CHECK = "網速顯示簡潔模式"
TXT_HIDE_PERCENTAGE_CHECK = "不顯示 % 符號"
TXT_SPECIFY_EACH_ITEM_COLOR_CHECK = "指定每個項目的色彩"
TXT_DOUBLE_CLICK_ACTION = "按兩下動作:"
TXT_SEPARATE_VALUE_UNIT_CHECK = "數值和單位用空格分隔"
TXT_NETSPEED_UNIT = "網速單位："
TXT_UNIT_BYTE_RADIO = "B (位元組)"
TXT_UNIT_BIT_RADIO = "b (位元)"
TXT_SHOW_TOOL_TIP_CHK = "顯示游標提示"
TXT_EXE_PATH_STATIC = "指定應用程式："
TXT_BROWSE_BUTTON = "瀏覽(&B)..."
TXT_DISPLAY_TEXT_SETTING_BUTTON = "顯示文字設定(&T)..."
TXT_MEMORY_DISPLAY_MODE = "記憶體顯示方式："
TXT_ALWAYS_ON_TOP_CHECK = "最上層顯示"
TXT_MOUSE_PENETRATE_CHECK = "游標穿透"
TXT_LOCK_WINDOW_POS_CHECK = "鎖定視窗位置"
TXT_ALOW_OUT_OF_BORDER_CHECK = "允許超出螢幕邊界"

; CMessageDlg
TXT_TITLE_MESSAGE_DLG = "消息"

; CNetworkInfoDlg
TXT_TITLE_NETWORK_INFO_DLG = "連線詳細資料"

; COptionsDlg
TXT_TITLE_OPTION = "設定選項"

; CPluginManagerDlg
TXT_TITLE_PLUGIN_MANAGE = "外掛管理"
TXT_OPTINS_BUTTON = "選項(&O)..."
TXT_PLUGIN_INFO_BUTTON = "詳細資訊(&I)..."
TXT_PLUGIN_DEV_GUID_STATIC = "外掛開發指南"
TXT_PLUGIN_DOWNLOAD_STATIC = "更多外掛下載"
TXT_OPEN_PLUGIN_DIR_STATIC = "開啟外掛目錄"

;CSelectConnectionsDlg
TXT_TITLE_SELECTION_CONNECTION = "選擇要監控的網路連線"

; CSetItemOrderDlg
TXT_TITLE_SELECT_ORDER_DIALOG = "顯示設定"
TXT_MOVE_UP_BUTTON = "上移"
TXT_MOVE_DOWN_BUTTON = "下移"
TXT_RESTORE_DEFAULT_BUTTON = "還原預設"

; CSkinAutoAdaptSettingDlg
TXT_TITLE_SKIN_AUTO_ADAPTT_DLG = "面板自動切換設定"
TXT_SKIN_IN_DARK_MODE = "深色Windows模式下使用的面板："
TXT_SKIN_IN_LIGHT_MODE = "淺色Windows模式下使用的面板："

; CSkinDlg
TXT_TITLE_SKIN_DLG = "更換面板"
TXT_PREVIEW_GROUP_STATIC = "預覽"
TXT_SELECT_A_SKIN = "選擇面板"
TXT_SKIN_AUTHOR = "面板作者"
TXT_SKIN_MAKING_UP_TUTORIAL = "面板製作教學"
TXT_DOWNLOAD_MORE_SKIN = "更多面板下載"
TXT_OPEN_SKIN_DIR = "開啟面板目錄"
TXT_SKIN_AUTO_ADAPT_CHECK = "根據Windows深淺色模式自動切換面板"
TXT_SKIN_AUTO_ADAPT_BUTTON = "自動切換設定..."

; CTaskbarColorDlg
TXT_TITLE_TASKBAR_COLOR_DLG = "工作列視窗色彩設定"

; CTaskBarSettingsDlg
TXT_PRESET = "預設方案(&P)"
TXT_BACKGROUND_COLOR = "背景色彩:"
TXT_BACKGROUND_TRANSPARENT = "背景透明"
TXT_AUTO_SET_BACK_COLOR = "根據工作列色彩自動設定背景色"
TXT_AUTO_ADAPT_LIGHT_THEME = "自動適應 Windows 10 深色/淺色主題"
TXT_AUTO_ADAPT_SETTINGS_BUTTON = "自動適應設定(&S)..."
TXT_DISPLAY_SETTINGS = "顯示設定"
TXT_DISPLAY_SETTINGS_BUTTON = "顯示設定(&O)..."
TXT_SPEED_SHORT_MODE = "網速顯示簡潔模式"
TXT_VALUE_RIGHT_ALIGN = "數值向右對齊"
TXT_NET_SPEED_DATA_WIDTH = "網速占用位數："
TXT_CHARACTORS = "字元"
TXT_HORIZONTAL_ARRANGE = "水平排列"
TXT_ITEM_SPACING = "條目間距"
TXT_VERTICAL_MARGIN = "垂直間距："
TXT_PIXELS = "像素"
TXT_TASKBAR_WINDOW = "工作列視窗"
TXT_TASKBAR_WND_ON_LEFT = "工作列視窗顯示在工作列的左側"
TXT_DISPLAY_TO_SHOW_TASKBAR_WND = "顯示工作列視窗的顯示器："
TXT_WIN11_SETTINGS_BUTTON = "Windows11 相關設定"
TXT_RESOURCE_USAGE_GRAPH = "資源佔用圖例"
TXT_SHOW_RESOURCE_USAGE_GRAPH = "顯示資源佔用圖例"
TXT_SHOW_DASHED_BOX = "顯示虛線框"
TXT_SHOW_NET_SPEED_GRAPH = "顯示網速佔用圖例"
TXT_NET_SPEED_GRAPH_MAX_VALUE = "網速佔用圖例最大值："
TXT_USAGE_GRAPH_COLOR = "佔用圖例色彩："
TXT_GRAPH_DISPLAY_MODE = "佔用圖例型別："
TXT_USAGE_GRAPH_FOLLOW_SYSTEM_CHECK = "跟隨 Windows 主題顏色"
TXT_BAR_MODE = "柱狀圖"
TXT_PLOT_MODE = "橫向捲動圖"
TXT_RENDERING_SETTINGS = "渲染設定"
TXT_RENDERING_SETTINGS_NOTE = "注意：只有在勾選“背景透明”且不勾選“根據工作列顏色自動設定背景色”時才會使用Direct2D渲染"
TXT_ENABLE_COLOR_EMOJI = "使用彩色emoji"

; CWin11TaskbarSettingDlg
TXT_TITLE_WIN11_TASKBAR_SETTING = "Windows11 相關設定"
TXT_TASKBAR_WINDOWS_CLOSE_TO_ICON = "工作列視窗靠近圖示而不是靠近工作列的兩側"
TXT_WINDOW_VERTICAL_OFFSET = "視窗垂直偏移："
TXT_WINDOW_HORIZONTAL_OFFSET = "視窗水平偏移："
TXT_AVOID_OVERLAP_RIGHT_WIDGETS_CHECK = "避免與右側小工具重疊（如果小工具出現在工作列的右側，請勾選此項）"
TXT_WIDGETS_WIDTH = "小工具宽度："

[menu]
; IDR_HISTORY_TRAFFIC_MENU
TXT_SHOW_SCALE = "顯示比例"
TXT_USE_LINEAR_SCALE = "使用線性比例"
TXT_USE_LOG_SCALE = "使用對數比例"
TXT_FIRST_DAT_OF_WEEEK = "一週的第一天"
TXT_SUNDAY = "星期日"
TXT_MONDAY = "星期一"
TXT_GO_TO_TODAY = "轉到今天"

; IDR_INFO_MENU
TXT_COPY_TEXT = "複製文字(&C)"

; IDR_MENU1
TXT_SELECT_CONNECTIONS = "選擇網路連線(&S)"
TXT_AUTO_SELECT = "自動選擇(&A)"
TXT_SELECT_ALL = "選擇全部(&S)"
TXT_CONNECTION_DETAILS = "連線細節(&I)"
TXT_ALWAYS_ON_TOP = "最上層顯示(&T)"
TXT_MOUSE_PENETRATE = "游標穿透(&U)"
TXT_LOCK_WINDOW_POS = "鎖定視窗位置(&L)"
TXT_SHOW_MORE_INFO = "顯示更多資訊(&C)"
TXT_SHOW_TASKBAR_WINDOW = "顯示工作列視窗(&W)"
TXT_SHOW_MAIN_WINDOW = "顯示主視窗(&M)"
TXT_WINDOW_OPACITY = "視窗不透明度(&P)"
TXT_OTHER_FUNCTIONS = "其他功能(&E)"
TXT_CHANGE_SKIN = "更換面板(&S)..."
TXT_CHANGE_NOTIFY_ICON = "更換通知區域圖示(&N)..."
TXT_ALLOW_OUT_OF_BOUNDARIES = "允許超出螢幕邊界(&B)"
TXT_HISTORY_TRAFFIC_STATISTICS = "歷史流量統計(&H)"
TXT_PLUGIN_MANAGE = "外掛管理(&P)..."
TXT_OPTIONS = "選項(&O)..."
TXT_HELP_MENU = "說明(&H)"
TXT_HELP = "說明(&H)"
TXT_FAQ = "常見問題(&F)"
TXT_UPDATE_LOG = "更新記錄(&L)"
TXT_ABOUT = "關於(&A)..."
TXT_CHECK_UPDATE = "檢查更新(&C)..."
TXT_EXIT = "結束(&X)"

; IDR_TASK_BAR_MENU
TXT_DISPLAY_SETTINGS = "顯示設定(&P)..."
TXT_CLOSE_TASKBAR_WINDOW = "關閉工作列視窗(&C)"
TXT_TASK_MANAGER = "工作管理員(&T)"

;IDR_PLUGIN_MANAGER_MENU
TXT_PLUGIN_DETAIL = "詳細資訊(&D)..."
TXT_PLUGIN_OPTIONS = "選項(&O)..."
TXT_PLUGIN_DISABLE = "停用(&I)"

;IDR_DISPLAY_ITEM_CONTEXT_MENU
TXT_RESTORE_DEFAULT = "還原預設(&R)"
