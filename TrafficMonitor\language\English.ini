﻿[general]
BCP_47 = "en-US"
DISPLAY_NAME = "English"
TRANSLATOR = ""
TRANSLATOR_URL = ""
DEFAULT_FONT = "Segoe UI"

[text]
; String Table
IDS_CHECK_UPDATE_FAILD = "Check for update failed, please check your network connection!"
IDS_CHECK_UPDATE_ERROR = "Check for update failed and get the wrong information from the remote update file, please contact the author!"
IDS_UPDATE_AVLIABLE = "New version V%s detected, do you want to go to update?"
IDS_UPDATE_AVLIABLE2 = "New version V%s detected, update content: \r\n%s\r\ndo you want to go to update?"
IDS_ALREADY_UPDATED = "Already updated to the latest version."
IDS_AUTORUN_FAILED_NO_KEY = "Cannot achieve auto run when Windows start, cannot find the corresponding key in the registry."
IDS_AUTORUN_FAILED_NO_ACCESS = "Registry key write failed, may not have permission to access the key!"
IDS_AUTORUN_DELETE_FAILED = "Registry key delete failed, may not have permission to access the key!"
IDS_AN_INSTANCE_RUNNING= "There is already an instance running."
IDS_TRAFFIC_USED_TODAY = "Traffic used today"
IDS_MEMORY_USAGE = "Memory usage"
IDS_CPU_USAGE = "CPU usage"
IDS_SEND_EMAIL_TO_ATHOUR= "Send an e-mail to the author."
IDS_GOTO_GITHUB = "Go to the page on GitHub for this project."
IDS_DONATE_ATHOUR = "Donate the author"
IDS_UPLOAD = "Upload"
IDS_DOWNLOAD = "Download"
IDS_MEMORY = "Memory"
IDS_UPLOAD_DISP = "UP"
IDS_DOWNLOAD_DISP = "DN"
IDS_MEMORY_DISP = "MEM"
IDS_CONNOT_SAVE_CONFIG_WARNING = "Warning: Unable to save settings, unable to write data to file= ""<%file_path%>""! Running this program as an administrator may solve this problem."
IDS_TRAFFICMONITOR = "TrafficMonitor"
IDS_INSUFFICIENT_BUFFER= "The size of the buffer used to store connection information is not enough, the connection has been reinitialized. (<%cnt%> times has been reinitialized)"
IDS_CONNECTION_NUM_CHANGED = "The connection number change has been detected. The connection has been retrieved. Previous connections: <%before%>, now connections: <%after%>. (<%cnt%> times has been reinitialized)"
IDS_CONNECTION_NOT_MATCH = "An exception may have occurred, the currently selected connection is inconsistent with the expected connection. The connection has been retrieved. (<%cnt%> times has been reinitialized)"
IDS_CONNOT_INSERT_TO_TASKBAR = "The window is not successfully embedded in the taskbar, it may be blocked by security software or the start menu is not closed, please try restarting explorer. TrafficMonitor will keep trying but stop reminding, error code:"
IDS_MEMORY_UDAGE_EXCEED= "Memory usage has been reached"
IDS_NOTIFY = "Notify"
IDS_TODAY_TRAFFIC_EXCEED = "Traffic used today has been reached"
IDS_DATE = "Date"
IDS_TRAFFIC_USED = "Total traffic"
IDS_FIGURE = "Figure"
IDS_DEFAULT_ICON = "Default Icon"
IDS_ICON = "Icon"
IDS_INTERFACE_NAME = "Interface name"
IDS_INTERFACE_DESCRIPTION= "Interface description"
IDS_CONNECTION_TYPE = "Connection type"
IDS_IF_TYPE_OTHER = "Other type of network"
IDS_IF_TYPE_ETHERNET_CSMACD= "Ethernet network"
IDS_IF_TYPE_ISO88025_TOKENRING= "Token ring network"
IDS_IF_TYPE_FDDI = "Fiber Distributed Data Interface (FDDI) network"
IDS_IF_TYPE_PPP = "PPP network"
IDS_IF_TYPE_SOFTWARE_LOOPBACK= "Software loopback network"
IDS_IF_TYPE_ATM = "ATM network"
IDS_IF_TYPE_IEEE80211 = "IEEE 802.11 wireless network"
IDS_IF_TYPE_TUNNEL = "Tunnel type encapsulation network"
IDS_IF_TYPE_IEEE1394 = "IEEE 1394 (Firewire) high performance serial bus network"
IDS_IF_TYPE_IEEE80216_WMAN= "Mobile broadband interface for WiMax devices"
IDS_IF_TYPE_WWANPP = "Mobile broadband interface for GSM-based devices"
IDS_IF_TYPE_WWANPP2 = "Mobile broadband interface for CDMA-based devices"
IDS_UNKNOW_CONNECTION = "Unknow network"
IDS_SPEED = "Speed"
IDS_ADAPTER_PHYSICAL_ADDRESS= "Adapter physical address"
IDS_IP_ADDRESS = "IP address"
IDS_SUBNET_MASK = "Subnet mask"
IDS_DEFAULT_GATEWAY = "Default gateway"
IDS_OPERATIONAL_STATUS = "Connection status"
IDS_IF_OPER_STATUS_NON_OPERATIONAL= "LAN adapter has been disabled"
IDS_IF_OPER_STATUS_UNREACHABLE= "WAN adapter that is not connected"
IDS_IF_OPER_STATUS_DISCONNECTED= "network cable disconnected or no carrier"
IDS_IF_OPER_STATUS_CONNECTING = "WAN adapter that is in the process of connecting"
IDS_IF_OPER_STATUS_CONNECTED = "WAN adapter that is connected to a remote peer"
IDS_IF_OPER_STATUS_OPERATIONAL= "LAN adapters is connected"
IDS_UNKNOW_STATUS = "Unknow status"
IDS_BYTES_RECEIVED = "Bytes received"
IDS_BYTES_SENT = "Bytes sent"
IDS_BYTES_RECEIVED_SINCE_START= "Bytes received since the program start"
IDS_BYTES_SENT_SINCE_START= "Bytes sent since the program start"
IDS_PROGRAM_ELAPSED_TIME= "Program elapsed time"
IDS_HOUR_MINUTE_SECOND = "%d hours, %d minutes, %d seconds"
IDS_INTERNET_IP_ADDRESS= "Internet IP address"
IDS_GET_FAILED = "Get failed"
IDS_ITEM = "Item"
IDS_VALUE = "Value"
IDS_COPY_TO_CLIPBOARD_FAILED= "Copy to clipboard failed!"
IDS_SKIN_AUTHOUR = "Skin author:"
IDS_SPEED_SHORT_MODE_TIP = "When checked, the network speed display will be reduced for 1 decimal place, and the unit does not show= ""B""."
IDS_AUTO = "Auto"
IDS_FIXED_AS = "Fixed as"
IDS_OPEN_CONNECTION_DETIAL= "Open connection details"
IDS_OPEN_HISTORICAL_TRAFFIC= "Open historical traffic statistics"
IDS_SHOW_HIDE_MORE_INFO= "Show/hide more info"
IDS_SHOW_HIDE_CPU_MEMORY= "Show/hide CPU and memory usage"
IDS_OPEN_OPTION_SETTINGS= "Open option settings"
IDS_OPEN_TASK_MANAGER = "Open task manager"
IDS_CHANGE_SKIN = "Change skin"
IDS_NONE = "None"
IDS_FONT_SIZE_WARNING = "The font size must be between %d and %d!"
IDS_SAME_TEXT_BACK_COLOR_WARNING = "Warning: The text color is the same as the background color!"
IDS_SAME_BACK_TEXT_COLOR_WARNING = "Warning: The background color is the same as the text color!"
IDS_FOLLOWING_SYSTEM = "Following system"
IDS_LANGUAGE_CHANGE_INFO = "Please restart the application to apply the language setting."
IDS_MAIN_WINDOW_SETTINGS= "Main Window Settings"
IDS_TASKBAR_WINDOW_SETTINGS= "Taskbar Window Settings"
IDS_GENERAL_SETTINGS = "General Settings"
IDS_ACQUIRING = "Acquiring"
IDS_LIST_VIEW = "List View"
IDS_CALENDAR_VIEW = "Calendar View"
IDS_MONDAY = "Mon"
IDS_TUESDAY = "Tue"
IDS_WEDNESDAY = "Wed"
IDS_THURSDAY = "Thu"
IDS_FRIDAY = "Fri"
IDS_SATURDAY = "Sat"
IDS_SUNDAY = "Sun"
IDS_CURRENT_MONTH_TOTAL_TRAFFIC= "Current month total traffic:"
IDS_TRAFFIC_USED1 = "Traffic used:"
IDS_CONNOT_INSERT_TO_TASKBAR_ERROR_LOG = "Failed to insert to the taskbar, retry <%cnt%> times. GetLastError(): <%error_code%>."
IDS_NO_CONNECTION = "No Connection"
IDS_CONTACT_TRANSLATOR = "Contact this translator."
IDS_THANKS_DONORS = "Thanks to the following donors:"
IDS_GET_URL_ERROR_LOG_INFO = "An error occurred when requesting= ""<%1%>"", error code: <%2%>."
IDS_SHOW_ALL_INFO_TIP = "When this item is checked, all network interfaces will be displayed in the= ""Select Network Connections"" list in the right-click menu. It is recommended that you check this item only when needed."
IDS_CFG_DIR_CHANGED_INFO = "You have changed the location where the configuration and data files saved. It will take effect after restarted. You may need to transfer the configuration and data files to the new location manually."
IDS_DOUBLE_CLICK_TO_ACQUIRE= "<Double-click here to acquire.>"
IDS_ERROR1 = "Error"
IDS_ERROR_MESSAGE = "Error Message:"
IDS_CRASH_INFO = "Sorry, the program has crashed. Please restart the program. \r\nIf you encounter this problem many times, please try to disable the hardware monitoring function in= ""Options"" >= ""General Settings"", and disable all plugins in the= ""Plugin Manager"" dialog box. \r\nIf the problem still exists, please email this file= ""<%1%>"" to <EMAIL> to help the author locate the problem. And add the following to the body of the message:"
IDS_TITLE_ACKNOWLEDGEMENT= "Acknowledgement"
IDS_SAVE_DEFAULT_STYLE_INQUIRY = "Are you sure you want to save the current taskbar color setting to= ""Preset <%1%>""?"
IDS_SPECIFIC_APP = "Open specified application"
IDS_EXE_FILTER = "Applications|*.exe|Batch files|*.bat||"
IDS_PRESET = "Preset"
IDS_LIGHT_MODE = "Light mode"
IDS_AUTO_ADAPT_TIP_INFO= "This function will automatically switch the color presets when Windows 10 dark/light themes changed. Click the= ""Auto Adapt settings"" button to configure the preset scheme for automatic switching."
IDS_WITHOUT_TEMPERATURE= "Lite"
IDS_MOUSE_PENETRATE_TIP_INFO = "Mouse penetrate is turned on. If you need to turn off mouse penetrate, locate trafficMonitor icon in the system notification area, click right mouse button, and turn off mouse penetrate in the menu. Click Cancel no longer prompt."
IDS_HISTORY_TRAFFIC_LOST_ERROR_LOG = "Historical traffic data loss was detected. The current number of records: <%1%>, <%2%> records were recovered from the backup file."
IDS_LEGEND = "Legend"
IDS_LICENSE_EXPLAIN = "Please follow the following open source protocol when using the source code of this software."
IDS_LICENSE = "License"
IDS_DAY_VIEW = "Day view"
IDS_MONTH_VIEW = "Month view"
IDS_QUARTER_VIEW = "Quarter view"
IDS_YEAR_VIEW = "Year view"
IDS_LINEAR_SCALE = "Linear scale"
IDS_LOG_SCALE = "Logarithmic scale"
IDS_CPU_TEMPERATURE = "CPU Temperature"
IDS_GPU_TEMPERATURE = "GPU Temperature"
IDS_CPU_FREQ = "CPU Freq"
IDS_HDD_TEMPERATURE = "Hard Disk Temperature"
IDS_MAINBOARD_TEMPERATURE= "Main Board Temperature"
IDS_GPU_DISP = "GPU"
IDS_HDD_DISP = "HDD"
IDS_MAINBOARD_DISP = "MBD"
IDS_CPU_TEMPERATURE_EXCEED= "CPU temperature has been reached"
IDS_GPU_TEMPERATURE_EXCEED= "GPU temperature has been reached"
IDS_HDD_TEMPERATURE_EXCEED= "Hard disk temperature has been reached"
IDS_MBD_TEMPERATURE_EXCEED= "Main board temperature has been reached"
IDS_MUSICPLAYER2_DESCRIPTION = "MusicPlayer2: A beautiful and easy-to-use local music player for Windows"
IDS_SIMPLENOTEPAD_DESCRIPTION = "SimpleNotePad: A simple text editor for Windows"
IDS_COLOR = "Color"
IDS_COLOR_LABEL = "Label color"
IDS_COLOR_VALUE = "Value color"
IDS_GPU_USAGE = "GPU usage"
IDS_IF_OPER_STATUS_UP = "Adapter is connected"
IDS_IF_OPER_STATUS_DOWN= "Adapter is not connected"
IDS_IF_OPER_STATUS_DORMANT= "Adapter is connecting"
IDS_GOTO_GITEE = "Go to the page on Gitee for this project."
IDS_USAGE_PERCENTAGE = "Used Percentage"
IDS_MEMORY_USED = "Memory used"
IDS_MEMORY_AVAILABLE = "Memory available"
IDS_DOTNET_NOT_INSTALLED_TIP = ".Net Framework v4.5.2 or higher is not installed in the system. The temperature monitoring function will not be available. Click Cancel to no longer prompt."
IDS_VERSION_UPDATE = "New version update"
IDS_AVREAGE_TEMPERATURE= "Average Temperature"
IDS_HARDWARE_MONITOR_WARNING = "Warning: You are turning on the hardware monitoring function. Hardware monitoring function can be used to display temperature and GPU usage information. Please read the following carefully before turning on the hardware monitoring function:\r\nTrafficMonitor is not a professional hardware monitoring software. It cannot guarantee that hardware information can be obtained on any computer, nor can it guarantee the accuracy of the obtained hardware information.\r\nThe hardware monitoring function is implemented by the third library LibreHardwareMonitor. After the hardware monitoring is turned on, some problems may occur in some computers, including but not limited to :\r\n* Abnormal CPU and memory usage\r\n* Program crash\r\n* Computer crush\r\nPlease decide to turn on the hardware monitoring function after you are aware of the above risks. \r\nDo you really want to turn on the hardware monitoring function?"
IDS_HDD_USAGE = "Hard Disk usage"
IDS_FILE_NAME = "File name"
IDS_STATUS = "Status"
IDS_PLUGIN_LOAD_SUCCEED= "Load succeed"
IDS_PLUGIN_MODULE_LOAD_FAILED = "Plug-in module load failed, error code: <%1%>"
IDS_PLUGIN_FUNCTION_GET_FAILED = "Function acquisition failed, error code: <%1%>"
IDS_PLUGIN_INFO = "Plugin details"
IDS_NAME = "Name"
IDS_DESCRIPTION = "Description"
IDS_FILE_PATH = "File path"
IDS_ITEM_NUM = "Displayed items number"
IDS_ITEM_NAMES = "Displayed items names"
IDS_AUTHOR = "Author"
IDS_COPYRIGHT = "Copyright"
IDS_PLUGIN_NO_OPTIONS_INFO= "The plug-in does not provide option settings."
IDS_PLUGIN_NAME = "Plug-in name"
IDS_DISABLED = "Disabled"
IDS_RESTART_TO_APPLY_CHANGE_INFO = "Please restart the program to apply this change."
IDS_VERSION = "Version"
IDS_DISP_ITEM_ID = "Displayed item ID"
IDS_PLUGIN_API_VERSION = "API version"
IDS_WEEK_VIEW = "Weekly view"
IDS_WEEK_NUM = "Week <%1%>"
IDS_URL = "Url"
IDS_PLUGIN_VERSION_NOT_SUPPORT= "The plug-in version is too low."
IDS_MODIFY_PRESET = "Modify Preset"
IDS_SELECT_AT_LEASE_ONE_WARNING= "Please select at least one!"
IDS_AUTO_SAVE_TO_PRESET_TIP = "When the= ""Automatically adapt to Windows 10 dark/light theme"" function is enabled, if this option is checked, when the taskbar window color, background color settings are changed, according to the current Windows dark/light theme, automatically save to the corresponding preset."
IDS_TOTAL_NET_SPEED = "Total speed"
IDS_SHOW_RESOURCE_USAGE_GRAPH_TIP = "Display resource usage graphs on CPU/memory hard disk utilization, temperature information, and plugin items."
IDS_SHOW_NET_SPEED_GRAPH_TIP = "Display net speed indicator on upload, download and total net speed."
IDS_REFRESH_CONNECTION_LIST= "Refresh connection list"
IDS_HARDWARE_MONITOR_INIT_FAILED = "Hardware monitoring function failed to initialize, hardware monitoring will not be available!"
IDS_HARDWARE_INFO_ACQUIRE_FAILED_ERROR = "Error getting hardware monitoring data!"
IDS_AUTO_RUN_METHOD_REGESTRY= "Auto-run mode : Registry"
IDS_AUTO_RUN_METHOD_TASK_SCHEDULE= "Auto-run mode: Task schedule"
IDS_PATH = "Path"
IDS_SET_AUTO_RUN_FAILED_WARNING= "Set auto-run at start up failed!"
IDS_UPDATE_TASKBARDLG_FAILED_TIP = "Failed to update the contents of the taskbar window, direct2d rendering has been disabled. Note: the settings window may not update this setting adjustment immediately."
IDS_D2DDRAWCOMMON_ERROR_TIP = "An error occurred during direct2d rendering and direct2d rendering has been disabled. Note: the settings window may not update this adjustment immediately."
IDS_PLUGIN_OPTIONS = "Plugin Options"
IDS_GET_CPU_USAGE_BY_PDH_FAILED_LOG = "Get CPU usage by performance counter failed, fullCounterPath=<%1%>"
IDS_PRIMARY_DISPLAY = "Primary display"
IDS_SECONDARY_DISPLAY = "Secondary display <%1%>"
IDS_RESTORE_FROM_SLEEP_LOG = "The system has been restored from hibernation, the connection has been reinitialized. (<%1%> times has been reinitialized.)"
IDS_PLUGIN_NEW_VERSION_INFO = "Update available, latest version: <%1%>"
IDS_TRAFFICMONITOR_PLUGIN_NITIFICATION = "TrafficMonitor Plugin Notification"

TXT_OK = "OK"
TXT_CANCEL = "Cancel"
TXT_CLOSE = "Close"
TXT_APPLY = "Apply"

; Text used for dialog. (Must be started with "TXT_")
; CAboutDlg
TXT_TITLE_ABOUT = "About TrafficMonitor"
TXT_ABOUT_VERSION = "TrafficMonitor<%1%>, V<%2%>"
TXT_ABOUT_COPYRIGHT = "Copyright (C) 2017-2025 By ZhongYang\nLast compiled date: <compile_date>"
TXT_ABOUT_TRANSLATOR = "<%1%> Translator: <%2%>"
TXT_ABOUT_THIRD_PARTY_LIB = "The third-party libraries used in this project:"
TXT_ABOUT_AUTHOR_S_OTHER_SOFTWARE = "The author's other software:"
TXT_ABOUT_CONTACT_AUTHOR = "Contact author"
TXT_ABOUT_LICENSE = "License"
TXT_ABOUT_ACKNOWLEDGEMENT = "Acknowledgement"
TXT_ABOUT_DONATE = "Donate"

; CAppAlreadyRuningDlg
TXT_TRAFFICMONITOR_ALREAD_RUNING = "TrafficMonitor is already running."
TXT_EXIT_THE_PROGRAM = "E&xit the program"
TXT_OPEN_OPTION_SETTINGS = "Open &option settings"
TXT_SHOW_HIDE_MAIN_WINDOW = "Show/hide &main window"
TXT_SHOW_HIDE_TASKBAR_WINDOW = "Show/hide &taskbar window"

; CAutoAdaptSettingsDlg
TXT_TITLE_AUTO_ADATP_SETTINGS = "Auto adapt settings"
TXT_COLOR_PRESET_IN_DARK_MODE = "Color preset used in dark Windows mode:"
TXT_COLOR_PRESET_IN_LIGHT_MODE = "Color preset used in light Windows mode:"
TXT_AUTO_SAVE_TO_PRESET_CHECK = "Auto-save taskbar window color settings to preset"

; CDisplayTextSettingDlg
TXT_TITLE_DISPLAY_TEXT_SETTING = "Display Text Settings"
TXT_RESTORE_DEFAULT = "Restore &Default for All"

; CDonateDlg
TXT_TITLE_DONATE = "Donate"
TXT_DONATE_INFO = "If you think this software is helpful to you, you can scan the following QR code through Alipay or WeChat Pay to donate the author to help the author to make this software better. Please feel free for the amount."

; CGeneralSettingsDlg
TXT_APPLICATION_SETTINGS = "Application Settings"
TXT_CHECK_UPDATE = "Check for update at startup"
TXT_CHECK_NOW = "&Check now"
TXT_UPDATE_SOURCE = "Update source:"
TXT_AUTO_RUN_CHECK = "Auto run when Windows starts"
TXT_RESET_AUTO_RUN_BUTTON = "Reset autorun"
TXT_LANGUAGE = "Language:"
TXT_CONFIGURATION_AND_DATA_FILES = "Configuration and data files"
TXT_SAVE_TO_APPDATA_RADIO = "Save to Appdata directory"
TXT_SAVE_TO_PROGRAM_DIR_RADIO = "Save to the program directory"
TXT_OPEN_CONFIG_PATH_BUTTON = "Open configuration file &directory"
TXT_NOTIFICATION_MESSAGE = "Notification Message"
TXT_TODAY_TRAFFIC_TIP_CHECK = "Notify when today traffic has been reached"
TXT_TODAY_TRAFFIC_BACK = " "
TXT_MEMORY_USAGE_TIP_CHECK = "Notify when memory usage has been reached"
TXT_MEMORY_USAGE_BACK = "%"
TXT_CPU_TEMP_TIP_CHECK = "Notify when CPU temperature has been reached"
TXT_CPU_TEMP_BACK = "°C"
TXT_GPU_TEMP_TIP_CHECK = "Notify when GPU temperature has been reached"
TXT_GPU_TEMP_BACK = "°C"
TXT_HDD_TEMP_TIP_CHECK = "Notify when Hard disk temperature has been reached"
TXT_HDD_TEMP_BACK = "°C"
TXT_MBD_TEMP_TIP_CHECK = "Notify when Main board temperature has been reached"
TXT_MBD_TEMP_BACK = "°C"
TXT_HARDWARE_MONITORING = "Hardware Monitoring"
TXT_CPU = "CPU"
TXT_GPU = "GPU"
TXT_HARD_DISK = "Hard disk"
TXT_MAIN_BOARD = "Main board"
TXT_SELECT_HDD_STATIC = "Select the hard disk to be monitored:"
TXT_SELECT_CPU_STATIC = "Select the CPU temperature to be monitored:"
TXT_ADVANCED = "Advanced"
TXT_SHOW_ALL_CONNECTION_CHECK = "Show all network connections"
TXT_SELECT_CONNECTIONS_BUTTON = "&Select the connection to monitor..."
TXT_CPU_ACQUISITION_METHOD = "CPU usage acquisition method:"
TXT_USE_CPU_TIME_RADIO = "Based on CPU time"
TXT_USE_PDH_RADIO = "Use the performance counter"
TXT_USE_HARDWARE_MONITOR_RADIO = "Use the hardware monitor"
TXT_MONITORING_INTERVALS = "Monitoring intervals:"
TXT_MILLISECONDS = "milliseconds"
TXT_RESTORE_DEFAULT_TIME_SPAN_BUTTON = "&Restore default"
TXT_PLUGIN_MANAGE_BUTTON = "Plug-in manage..."
TXT_DISPLAY = "Display"
TXT_SHOW_NOTIFY_ICON_CHECK = "Show &Notify Icon"

; CHistoryTrafficCalendarDlg
TXT_YEAR = "Year:"
TXT_MONTH = "Month:"

; CHistoryTrafficDlg
TXT_TITLE_HISTORY_TRAFFIC = "Historical Traffic Statistics"

; CHistoryTrafficListDlg
TXT_VIEW_TYPE = "View type:"
TXT_FIGURE_VIEW_SCALE = "Figure view scale:"

; CIconSelectDlg
TXT_TITLE_CHANGE_ICON = "Change Notify Icon"
TXT_SELECT_A_ICON = "Select an icon:"
TXT_PREVIEW = "Preview"
TXT_NOTIFY_ICON_AUTO_ADAPT_CHECK = "Auto adapt by Windows 10 dark/light mode"

; CMainWndColorDlg
TXT_TITLE_MAIN_COLOR_DIALOG = "Main Window Color Settings"

; CMainWndSettingsDlg
TXT_FULLSCREEN_HIDE_CHECK = "Hide main window when program is running full screen"
TXT_COLOR_AND_FONT = "Color and Font"
TXT_FONT = "Font:"
TXT_FONT_SIZE = "Font size:"
TXT_SET_FONT_BUTTON = "Choose &Font..."
TXT_TEXT_COLOR = "Text color:"
TXT_RESOTRE_SKIN_DEFAULT_BUTTON = "&Restore skin default"
TXT_DISPLAY_TEXT = "Display Text"
TXT_SWITCH_UP_DOWN_CHECK = "Swap the position of upload and download"
TXT_UNIT_SETTINGS = "Unit Settings"
TXT_UNIT_SELECTION = "Unit selection:"
TXT_HIDE_UNIT_CHECK = "Don't show speed unit"
TXT_SPEED_SHORT_MODE_CHECK = "Short mode for netspeed display"
TXT_HIDE_PERCENTAGE_CHECK = "Don't show percent"
TXT_SPECIFY_EACH_ITEM_COLOR_CHECK = "Specify colors for each item"
TXT_DOUBLE_CLICK_ACTION = "Double click action:"
TXT_SEPARATE_VALUE_UNIT_CHECK = "Separate value and unit with space"
TXT_NETSPEED_UNIT = "Netspeed unit:"
TXT_UNIT_BYTE_RADIO = "B (Byte)"
TXT_UNIT_BIT_RADIO = "b (bit)"
TXT_SHOW_TOOL_TIP_CHK = "Show mouse tooltip"
TXT_EXE_PATH_STATIC = "Specified application:"
TXT_BROWSE_BUTTON = "&Browse..."
TXT_DISPLAY_TEXT_SETTING_BUTTON = "Display &Text settings..."
TXT_MEMORY_DISPLAY_MODE = "Memory display mode:"
TXT_ALWAYS_ON_TOP_CHECK = "Always on top"
TXT_MOUSE_PENETRATE_CHECK = "Mouse penetrate"
TXT_LOCK_WINDOW_POS_CHECK = "Lock window position"
TXT_ALOW_OUT_OF_BORDER_CHECK = "Allow out of screen boundaries"

; CMessageDlg
TXT_TITLE_MESSAGE_DLG = "Message"

; CNetworkInfoDlg
TXT_TITLE_NETWORK_INFO_DLG = "Connection Details"

; COptionsDlg
TXT_TITLE_OPTION = "Option Settings"

; CPluginManagerDlg
TXT_TITLE_PLUGIN_MANAGE = "Plug-in Manage"
TXT_OPTINS_BUTTON = "&Options..."
TXT_PLUGIN_INFO_BUTTON = "&Details..."
TXT_PLUGIN_DEV_GUID_STATIC = "Plug-in development guide"
TXT_PLUGIN_DOWNLOAD_STATIC = "Download more plug-ins"
TXT_OPEN_PLUGIN_DIR_STATIC = "Open plugin directory"

;CSelectConnectionsDlg
TXT_TITLE_SELECTION_CONNECTION = "Select the connection to monitor"

; CSetItemOrderDlg
TXT_TITLE_SELECT_ORDER_DIALOG = "Display settings"
TXT_MOVE_UP_BUTTON = "Move Up"
TXT_MOVE_DOWN_BUTTON = "Move Down"
TXT_RESTORE_DEFAULT_BUTTON = "Restore &Default"

; CSkinAutoAdaptSettingDlg
TXT_TITLE_SKIN_AUTO_ADAPTT_DLG = "Skin auto switching settings"
TXT_SKIN_IN_DARK_MODE = "Skin used in dark Windows mode:"
TXT_SKIN_IN_LIGHT_MODE = "Skin used in light Windows mode:"

; CSkinDlg
TXT_TITLE_SKIN_DLG = "Change Skin"
TXT_PREVIEW_GROUP_STATIC = "Preview"
TXT_SELECT_A_SKIN = "Select a skin"
TXT_SKIN_AUTHOR = "Skin author:"
TXT_SKIN_MAKING_UP_TUTORIAL = "Skin making-up tutorial"
TXT_DOWNLOAD_MORE_SKIN = "Download more skins"
TXT_OPEN_SKIN_DIR = "Open skin directory"
TXT_SKIN_AUTO_ADAPT_CHECK = "Automatically switch skins according to Windows dark / light color modes."
TXT_SKIN_AUTO_ADAPT_BUTTON = "Auto switching settings..."

; CTaskbarColorDlg
TXT_TITLE_TASKBAR_COLOR_DLG = "Taskbar window color settings"

; CTaskBarSettingsDlg
TXT_PRESET = "&Preset"
TXT_BACKGROUND_COLOR = "Background color:"
TXT_BACKGROUND_TRANSPARENT = "Background transparent"
TXT_AUTO_SET_BACK_COLOR = "Automatically set the background color according to the taskbar color"
TXT_AUTO_ADAPT_LIGHT_THEME = "Auto adapt to Windows 10 dark/light themes"
TXT_AUTO_ADAPT_SETTINGS_BUTTON = "Auto Adapt &settings..."
TXT_DISPLAY_SETTINGS = "Display Settings"
TXT_DISPLAY_SETTINGS_BUTTON = "&Display settings..."
TXT_SPEED_SHORT_MODE = "Short mode for netspeed display"
TXT_VALUE_RIGHT_ALIGN = "Align values to the right"
TXT_NET_SPEED_DATA_WIDTH = "Net speed data width:"
TXT_CHARACTORS = "characters"
TXT_HORIZONTAL_ARRANGE = "Horizontal arrange"
TXT_ITEM_SPACING = "Item spacing:"
TXT_VERTICAL_MARGIN = "Vertical Margin:"
TXT_PIXELS = "pixels"
TXT_TASKBAR_WINDOW = "Taskbar window"
TXT_TASKBAR_WND_ON_LEFT = "Taskbar window appears to the left of the taskbar"
TXT_DISPLAY_TO_SHOW_TASKBAR_WND = "The display to show taskbar window:"
TXT_WIN11_SETTINGS_BUTTON = "Settings related to Windows11"
TXT_RESOURCE_USAGE_GRAPH = "Resource usage graph"
TXT_SHOW_RESOURCE_USAGE_GRAPH = "Show resource usage graph"
TXT_SHOW_DASHED_BOX = "Show dashed box"
TXT_SHOW_NET_SPEED_GRAPH = "Show net speed graph"
TXT_NET_SPEED_GRAPH_MAX_VALUE = "The max value of the net speed graph:"
TXT_USAGE_GRAPH_COLOR = "Usage graph color:"
TXT_USAGE_GRAPH_FOLLOW_SYSTEM_CHECK = "Following Windows theme color"
TXT_GRAPH_DISPLAY_MODE = "Graph display mode:"
TXT_BAR_MODE = "Bar mode"
TXT_PLOT_MODE = "Plot mode"
TXT_RENDERING_SETTINGS = "Rendering settings"
TXT_RENDERING_SETTINGS_NOTE = "Note: Direct2D rendering is only used when ""Background transparent"" is checked but ""Automatically set the background color according to the taskbar color"" is not checked."
TXT_ENABLE_COLOR_EMOJI = "Enable colorful emoji"

; CWin11TaskbarSettingDlg
TXT_TITLE_WIN11_TASKBAR_SETTING = "Settings related to Windows11"
TXT_TASKBAR_WINDOWS_CLOSE_TO_ICON = "Taskbar window closed to the icon instead of the sides of the taskbar"
TXT_WINDOW_VERTICAL_OFFSET = "Window vertical offset:"
TXT_WINDOW_HORIZONTAL_OFFSET = "Window horizontal offset:"
TXT_AVOID_OVERLAP_RIGHT_WIDGETS_CHECK = "Avoid overlapping with right Widgets (Check this option if the Widgets appears on the right side of the taskbar)"
TXT_WIDGETS_WIDTH = "Widgets width:"

[menu]
; IDR_HISTORY_TRAFFIC_MENU
TXT_SHOW_SCALE = "Show Scale"
TXT_USE_LINEAR_SCALE = "Use Linear Scale"
TXT_USE_LOG_SCALE = "Use Logarithmic Scale"
TXT_FIRST_DAT_OF_WEEEK = "The first day of the week"
TXT_SUNDAY = "Sunday"
TXT_MONDAY = "Monday"
TXT_GO_TO_TODAY = "Go to today"

; IDR_INFO_MENU
TXT_COPY_TEXT = "&Copy Text"

; IDR_MENU1
TXT_SELECT_CONNECTIONS = "&Select Network Connections"
TXT_AUTO_SELECT = "&Auto Select"
TXT_SELECT_ALL = "&Select All"
TXT_CONNECTION_DETAILS = "Connection &Details"
TXT_ALWAYS_ON_TOP = "Always on &Top"
TXT_MOUSE_PENETRATE = "Mo&use Penetrate"
TXT_LOCK_WINDOW_POS = "&Lock Window Position"
TXT_SHOW_MORE_INFO = "Show More &Info"
TXT_SHOW_TASKBAR_WINDOW = "Show Taskbar &Window"
TXT_SHOW_MAIN_WINDOW = "Show &Main Window"
TXT_WINDOW_OPACITY = "Window O&pacity"
TXT_OTHER_FUNCTIONS = "Other &Functions"
TXT_CHANGE_SKIN = "Change &Skin..."
TXT_CHANGE_NOTIFY_ICON = "Change &Notify Icon..."
TXT_ALLOW_OUT_OF_BOUNDARIES = "Allow Out of Screen &Boundaries"
TXT_HISTORY_TRAFFIC_STATISTICS = "&Historical Traffic Statistics"
TXT_PLUGIN_MANAGE = "Plugin-in Manage..."
TXT_OPTIONS = "&Options..."
TXT_HELP_MENU = "&Help"
TXT_HELP = "&Help"
TXT_FAQ = "&Frequently Asked Questions"
TXT_UPDATE_LOG = "Update &Log"
TXT_ABOUT = "&About..."
TXT_CHECK_UPDATE = "&Check for Update..."
TXT_EXIT = "E&xit"

; IDR_TASK_BAR_MENU
TXT_DISPLAY_SETTINGS = "Dis&play Settings..."
TXT_CLOSE_TASKBAR_WINDOW = "&Close Taskbar Window"
TXT_TASK_MANAGER = "&Task Manager"

;IDR_PLUGIN_MANAGER_MENU
TXT_PLUGIN_DETAIL = "&Details..."
TXT_PLUGIN_OPTIONS = "&Options..."
TXT_PLUGIN_DISABLE = "D&isabled"

;IDR_DISPLAY_ITEM_CONTEXT_MENU
TXT_RESTORE_DEFAULT = "&Restore Default"
