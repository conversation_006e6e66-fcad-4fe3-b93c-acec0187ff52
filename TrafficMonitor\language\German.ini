﻿[general]
BCP_47 = "de-DE"
DISPLAY_NAME = "Deutsch"
TRANSLATOR = "bege10"
TRANSLATOR_URL = ""
DEFAULT_FONT = "Segoe UI"

[text]
; String Table
IDS_CHECK_UPDATE_FAILD = "Update-Check fehlge<PERSON>lagen, bitte die Internetverbindung prüfen!"
IDS_CHECK_UPDATE_ERROR = "Update-Check fehlgeschlagen und falsche Information von der serverseitigen Update-Datei erhalten, bitte kontaktiere den Entwickler!"
IDS_UPDATE_AVLIABLE = "Neue Version V%s erkannt, willst du updaten?"
IDS_UPDATE_AVLIABLE2 = "Neue Version V%s erkannt, Update-Inhalt: \r\n%s\r\nwillst du updaten?"
IDS_ALREADY_UPDATED = "Dies ist schon die neueste Version."
IDS_AUTORUN_FAILED_NO_KEY = "Kann automatischen Start beim Start von Windows nicht erlangen, kann den entsprechenden Schlüssel in der Registry nicht finden."
IDS_AUTORUN_FAILED_NO_ACCESS = "Schreiben des Registryschlüssels fehlgeschlagen, könnte keine Berechtigung haben auf den Schlüssel zuzugreifen!"
IDS_AUTORUN_DELETE_FAILED = "Löschen des Registryschlüssels fehlgeschlagen, könnte keine Berechtigung haben auf den Schlüssel zuzugreifen!"
IDS_AN_INSTANCE_RUNNING= "Es läuft bereits eine Instanz."
IDS_TRAFFIC_USED_TODAY = "Heute genutzter Verkehr"
IDS_MEMORY_USAGE = "Speicherauslastung"
IDS_CPU_USAGE = "CPU-Auslastung"
IDS_SEND_EMAIL_TO_ATHOUR= "Schicke eine E-Mail an den Entwickler."
IDS_GOTO_GITHUB = "Gehe zur GitHub-Seite dieses Projekts."
IDS_DONATE_ATHOUR = "Spende an den Entwickler"
IDS_UPLOAD = "Upload"
IDS_DOWNLOAD = "Download"
IDS_MEMORY = "Speicher"
IDS_UPLOAD_DISP = "UP"
IDS_DOWNLOAD_DISP = "DN"
IDS_MEMORY_DISP = "MEM"
IDS_CONNOT_SAVE_CONFIG_WARNING = "Warnung: kann die Einstellungen nicht speichern, kann Daten nicht in Datei= ""<%file_path%>"" schreiben! Dieses Programm als Administrator auszuführen könnte das Problem lösen."
IDS_TRAFFICMONITOR = "TrafficMonitor"
IDS_INSUFFICIENT_BUFFER= "Die Puffergröße für die Speicherung der Verbindungsinformation reicht nicht aus, die Verbindung wurde neu initialisiert. (<%cnt%> mal neu initialisiert)"
IDS_CONNECTION_NUM_CHANGED = "Eine Änderung der Verbindungsnummer wurde erkannt. Die Verbindung wurde wiederhergestellt. Vorherige Verbindungen: <%before%>, Jetzige Verbindungen: <%after%>. (<%cnt%> mal neu initialisiert)"
IDS_CONNECTION_NOT_MATCH = "Ein Ausnahmefall ist eingetreten, die kürzlich ausgewählte Verbindung ist inkonsistent der erwarteten Verbindung. Die Verbindung wurde wiederhergestellt. (<%cnt%> mal neu initialisiert)"
IDS_CONNOT_INSERT_TO_TASKBAR = "Das Fenster ist nicht erfolgreich in die Taskbar eingebettet, es könnte durch Sicherheitssoftware blockiert sein, oder das Startmenü ist nicht geschlossen. Bitte versuche den Explorer neu zu starten. TrafficMonitor wird es weiter versuchen, aber nicht weiter erinnern. Fehlercode:"
IDS_MEMORY_UDAGE_EXCEED= "Die Speicherauslastung wurde erreicht"
IDS_NOTIFY = "Benachrichtigen"
IDS_TODAY_TRAFFIC_EXCEED = "Die heutige Nutzung wurde erreicht"
IDS_DATE = "Datum"
IDS_TRAFFIC_USED = "Verkehr gesamt"
IDS_FIGURE = "Diagramm"
IDS_DEFAULT_ICON = "Standardsymbol"
IDS_ICON = "Symbol"
IDS_INTERFACE_NAME = "Schnittstellenname"
IDS_INTERFACE_DESCRIPTION= "Schnittstellenbeschreibung"
IDS_CONNECTION_TYPE = "Verbindungstyp"
IDS_IF_TYPE_OTHER = "Anderer Netzwerktyp"
IDS_IF_TYPE_ETHERNET_CSMACD= "Ethernet-Netzwerk"
IDS_IF_TYPE_ISO88025_TOKENRING= "Token-Ring-Netzwerk"
IDS_IF_TYPE_FDDI = "Fiber Distributed Data Interface (FDDI) Netzwerk"
IDS_IF_TYPE_PPP = "PPP-Netzwerk"
IDS_IF_TYPE_SOFTWARE_LOOPBACK= "Software-Loopback-Netzwerk"
IDS_IF_TYPE_ATM = "ATM Netzwerk"
IDS_IF_TYPE_IEEE80211 = "IEEE 802.11 Drahtlos-Netzwerk"
IDS_IF_TYPE_TUNNEL = "Tunnel type encapsulation Netzwerk"
IDS_IF_TYPE_IEEE1394 = "IEEE 1394 (Firewire) Hochleistungs-Serial-Bus-Netzwerk"
IDS_IF_TYPE_IEEE80216_WMAN= "Mobile Breitbandschnittstelle für WiMax-Geräte"
IDS_IF_TYPE_WWANPP = "Mobile Breitbandschnittstelle für GSM-basierte Geräte"
IDS_IF_TYPE_WWANPP2 = "Mobile Breitbandschnittstelle für CDMA-basierte Geräte"
IDS_UNKNOW_CONNECTION = "Unbekanntes Netzwerk"
IDS_SPEED = "Geschwindigkeit"
IDS_ADAPTER_PHYSICAL_ADDRESS= "Physische Adresse des Adapters"
IDS_IP_ADDRESS = "IP-Adresse"
IDS_SUBNET_MASK = "Subnetzmaske"
IDS_DEFAULT_GATEWAY = "Standardgateway"
IDS_OPERATIONAL_STATUS = "Verbindungsstatus"
IDS_IF_OPER_STATUS_NON_OPERATIONAL= "LAN-Adapter wurde deaktiviert"
IDS_IF_OPER_STATUS_UNREACHABLE= "WAN-Adapter ist nicht verbunden"
IDS_IF_OPER_STATUS_DISCONNECTED= "Netzwerkkabel nicht verbunden oder kein Anbieter"
IDS_IF_OPER_STATUS_CONNECTING = "WAN-Adapter stellt Verbindung her"
IDS_IF_OPER_STATUS_CONNECTED = "WAN-Adapter mit Remote-Peer verbunden"
IDS_IF_OPER_STATUS_OPERATIONAL= "LAN-Adapters ist verbunden"
IDS_UNKNOW_STATUS = "Unbekannter Status"
IDS_BYTES_RECEIVED = "Bytes empfangen"
IDS_BYTES_SENT = "Bytes gesendet"
IDS_BYTES_RECEIVED_SINCE_START= "Bytes empfangen seit Programmstart"
IDS_BYTES_SENT_SINCE_START= "Bytes gesendet seit Programmstart"
IDS_PROGRAM_ELAPSED_TIME= "Vergangene Programmzeit"
IDS_HOUR_MINUTE_SECOND = "%d Stunden, %d Minuten, %d Sekunden"
IDS_INTERNET_IP_ADDRESS= "Internet IP-Adresse"
IDS_GET_FAILED = "Ermittlung fehlgeschlagen"
IDS_ITEM = "Element"
IDS_VALUE = "Wert"
IDS_COPY_TO_CLIPBOARD_FAILED= "Kopieren in die Zwischenablage fehlgeschlagen!"
IDS_SKIN_AUTHOUR = "Skin-Autor/in:"
IDS_OVERWRITE_FONT_TEXT_WARNING = "Hinweis: Dieses Skin überschreibt die Schrift- und Anzeigetext-Einstellungen."
IDS_OVERWRITE_FONT_WARNING= "Hinweis: Dieses Skin überschreibt die Schrifteinstellungen."
IDS_OVERWRITE_TEXT_WARNING = "Hinweis: Dieses Skin überschreibt die Anzeigetext-Einstellungen."
IDS_SPEED_SHORT_MODE_TIP = "Wenn ausgewählt, wird die Anzeige der Netzgeschwindigkeit auf 1 Dezimalstelle reduziert und die Einheit zeigt nicht= ""B""."
IDS_AUTO = "Auto"
IDS_FIXED_AS = "Als"
IDS_OPEN_CONNECTION_DETIAL= "Verbindungsdetails öffnen"
IDS_OPEN_HISTORICAL_TRAFFIC= "Datenverbrauchsstatistik öffnen"
IDS_SHOW_HIDE_MORE_INFO= "Mehr Infos anzeigen/verstecken"
IDS_SHOW_HIDE_CPU_MEMORY= "CPU- und Speicherauslastung anzeigen/verstecken"
IDS_OPEN_OPTION_SETTINGS= "Einstellungen öffnen"
IDS_OPEN_TASK_MANAGER = "Taskmanager öffnen"
IDS_CHANGE_SKIN = "Skin wechseln"
IDS_NONE = "None"
IDS_FONT_SIZE_WARNING = "Die Schriftgröße muss zwischen %d und %d sein!"
IDS_SAME_TEXT_BACK_COLOR_WARNING = "Warnung: Die Textfarbe ist identisch mit der Hintergrundfarbe!"
IDS_SAME_BACK_TEXT_COLOR_WARNING = "Warnung: Die Hintergrundfarbe ist identisch mit der Textfarbe!"
IDS_FOLLOWING_SYSTEM = "Dem System folgen"
IDS_LANGUAGE_CHANGE_INFO = "Bitte das Programm neu starten, um die Spracheinstellung zu übernehmen."
IDS_MAIN_WINDOW_SETTINGS= "Hauptfenstereinstellungen"
IDS_TASKBAR_WINDOW_SETTINGS= "Taskbarfenster-Einstellungen"
IDS_GENERAL_SETTINGS = "Allgemeine Einstellungen"
IDS_ACQUIRING = "Erfassung"
IDS_LIST_VIEW = "Listenanzeige"
IDS_CALENDAR_VIEW = "Kalenderanzeige"
IDS_MONDAY = "Mo"
IDS_TUESDAY = "Di"
IDS_WEDNESDAY = "Mi"
IDS_THURSDAY = "Do"
IDS_FRIDAY = "Fr"
IDS_SATURDAY = "Sa"
IDS_SUNDAY = "So"
IDS_CURRENT_MONTH_TOTAL_TRAFFIC= "Gesamtverkehr aktueller Monat:"
IDS_TRAFFIC_USED1 = "Verbrauch:"
IDS_CONNOT_INSERT_TO_TASKBAR_ERROR_LOG = "Einfügen in die Taskbar fehlgeschlagen, <%cnt%> wiederholte Versuche. GetLastError(): <%error_code%>."
IDS_NO_CONNECTION = "Keine Verbindung"
IDS_CONTACT_TRANSLATOR = "Kontaktiere diese/n Übersetzer/in."
IDS_THANKS_DONORS = "Danke an die folgenden Spender/innen:"
IDS_GET_URL_ERROR_LOG_INFO = "Ein Fehler trat auf bei der Anforderung= ""<%1%>"", Fehlercode: <%2%>."
IDS_SHOW_ALL_INFO_TIP = "Wenn diese Option aktiviert ist, werden alle Netzwerkschnittstellen in der= ""Wähle Netzwerkverbindungen""-Liste im Rechtsklickmenü angezeigt. Es wird empfohlen dies nur bei Bedarf zu aktivieren."
IDS_CFG_DIR_CHANGED_INFO = "Du hast den Speicherort der Konfigurations- und Datendateien geändert. Diese wird nach einem Neustart übernommen. Eventuell musst du die Konfigurations- und Datendateien manuell an den neuen Ort verschieben."
IDS_DOUBLE_CLICK_TO_ACQUIRE= "<Hier doppelklicken zur Ermittlung.>"
IDS_ERROR1 = "Fehler"
IDS_ERROR_MESSAGE = "Fehlermeldung:"
IDS_CRASH_INFO = "Entschuldigung, das Programm ist abgestürzt. Bitte starte das Programm neu. \r\nWenn das Problem öfter auftritt, deaktiviere bitte die Hardwareüberwachungsfunktion in= ""Optionen"" >= ""Allgemeine Einstellungen"", und deaktiviere alle Plugins im= ""Plugin-Manager"" Dialogfenster. \r\nWenn das Problem weiter existiert, schicke diese Datei= ""<%1%>"" an <EMAIL> um dem Entwickler zu helfen das Problem zu lokalisieren. Und füge Folgendes dem Nachrichtentext hinzu:"
IDS_TITLE_ACKNOWLEDGEMENT= "Anerkennung"
IDS_SAVE_DEFAULT_STYLE_INQUIRY = "Bist du sicher, dass du die aktuelle Taskbarfarbeinstellung in= ""Voreinstellung <%1%>"" speichern willst?"
IDS_SPECIFIC_APP = "Spezifizierte Anwendung öffnen"
IDS_EXE_FILTER = "Programme|*.exe|Batch-Dateien|*.bat||"
IDS_PRESET = "Voreinstellung"
IDS_LIGHT_MODE = "Heller Modus"
IDS_AUTO_ADAPT_TIP_INFO= "Diese Funktion wird automatisch die Farbvoreinstellungen umschalten, wenn sich die Windows 10 Hell/Dunkel-Themes ändern. Klicke den= ""Auto-Anpassen-Einstellungen"" Button um das Voreinstellungsschema für automatisches Umschalten zu konfigurieren."
IDS_WITHOUT_TEMPERATURE= "Lite"
IDS_MOUSE_PENETRATE_TIP_INFO = "Durchklicken mit der Maus ist aktiviert. Um diese Funktion auszuschalten, klicke mit der rechten Maustaste auf das TrafficMonitor-Symbol im Systembenachrichtigungsbereich und deaktiviere die Funktion im Menü. Klicke auf Abbrechen um nicht mehr benachrichtigt zu werden."
IDS_HISTORY_TRAFFIC_LOST_ERROR_LOG = "Die Daten der Nutzungsstatistik sind verloren gegangen. Aktuelle Anzahl an Datensätzen: <%1%>, <%2%> wurden aus der Sicherungsdatei wiederhergestellt."
IDS_LEGEND = "Legende"
IDS_LICENSE_EXPLAIN = "Please follow the following open source protocol when using \r\nthe source code of this software."
IDS_LICENSE = "Lizenz"
IDS_DAY_VIEW = "Tag"
IDS_MONTH_VIEW = "Monat"
IDS_QUARTER_VIEW = "Vierteljahr"
IDS_YEAR_VIEW = "Jahr"
IDS_LINEAR_SCALE = "Linearer Maßstab"
IDS_LOG_SCALE = "Logarithmischer Maßstab"
IDS_CPU_TEMPERATURE = "CPU-Temperatur"
IDS_GPU_TEMPERATURE = "GPU-Temperatur"
IDS_CPU_FREQ = "CPU_Freq"
IDS_HDD_TEMPERATURE = "Festplattentemperatur"
IDS_MAINBOARD_TEMPERATURE= "Mainboard-Temperatur"
IDS_GPU_DISP = "GPU"
IDS_HDD_DISP = "HDD"
IDS_MAINBOARD_DISP = "MBD"
IDS_CPU_TEMPERATURE_EXCEED= "Die CPU-Temperatur wurde erreicht"
IDS_GPU_TEMPERATURE_EXCEED= "Die GPU-Temperatur wurde erreicht"
IDS_HDD_TEMPERATURE_EXCEED= "Die Festplattentemperatur wurde erreicht"
IDS_MBD_TEMPERATURE_EXCEED= "Die Mainboard-Temperatur wurde erreicht"
IDS_MUSICPLAYER2_DESCRIPTION = "MusicPlayer2: Ein schöner und einfach zu bedienender lokaler Musik-Player für Windows"
IDS_SIMPLENOTEPAD_DESCRIPTION = "SimpleNotePad: Ein einfacher Texteditor für Windows"
IDS_COLOR = "Farbe"
IDS_COLOR_LABEL = "Beschriftungsfarbe"
IDS_COLOR_VALUE = "Wertfarbe"
IDS_GPU_USAGE = "GPU-Auslastung"
IDS_IF_OPER_STATUS_UP = "Der Adapter ist verbunden"
IDS_IF_OPER_STATUS_DOWN= "Der Adapter ist nicht verbunden"
IDS_IF_OPER_STATUS_DORMANT= "Der Adapter verbindet sich"
IDS_GOTO_GITEE = "Gehe auf die Gitee-Seite dieses Projekts."
IDS_USAGE_PERCENTAGE = "Genutzter Prozentsatz"
IDS_MEMORY_USED = "Genutzter Speicher"
IDS_MEMORY_AVAILABLE = "Verfügbarer Speicher"
IDS_DOTNET_NOT_INSTALLED_TIP = ".Net Framework v4.5.2 oder höher ist auf dem System nicht installiert. Die Temperaturüberwachung ist nicht verfügbar. Klicke auf Abbrechen, um nicht mehr gefragt zu werden."
IDS_VERSION_UPDATE = "Update auf neue Version"
IDS_AVREAGE_TEMPERATURE= "Durchschnittstemperatur"
IDS_HARDWARE_MONITOR_WARNING = "Warnung: Du schaltest die Hardwareüberwachung ein. Die Hardwareüberwachung kann genutzt werden für die Anzeige der Temperatur und der GPU-Nutzung. Bitte lies das folgende sorgfältig durch, bevor du die Hardwareüberwachung anschaltest:\r\nTrafficMonitor ist keine professionelle Hardwareüberwachungssoftware. Sie kann nicht weder garantieren, dass auf jedem Computer Hardwareinformationen erhalten werden können, noch die Genauigkeit der erhaltenen Hardwareinformationen.\r\nDie Hardwareüberwachung ist implementiert mit der Third-Party-Bibliothek LibreHardwareMonitor. Nachdem die Hardwareüberwachung eingeschaltet ist, können auf manchen Computern Probleme entstehen, einschließlich aber nicht beschränkt auf:\r\n* Abnormale CPU- und Speichernutzung\r\n* Programmabsturz\r\n* Computerabsturz\r\nBitte entscheide dich die Hardwareüberwachung einzuschalten, nachdem du dir der obigen Risiken bewusst bist. \r\nWillst du die Hardwareüberwachung wirklich einschalten?"
IDS_HDD_USAGE = "Festplattennutzung"
IDS_FILE_NAME = "Dateiname"
IDS_STATUS = "Status"
IDS_PLUGIN_LOAD_SUCCEED= "Erfolgreich geladen"
IDS_PLUGIN_MODULE_LOAD_FAILED = "Laden des Plugin-Moduls fehlgeschlagen, Fehlercode: <%1%>"
IDS_PLUGIN_FUNCTION_GET_FAILED = "Funktionserfassung fehlgeschlagen, Fehlercode: <%1%>"
IDS_PLUGIN_INFO = "Plugin-Details"
IDS_NAME = "Name"
IDS_DESCRIPTION = "Beschreibung"
IDS_FILE_PATH = "Dateipfad"
IDS_ITEM_NUM = "Anzahl angezeigter Elemente"
IDS_ITEM_NAMES = "Namen angezeigter Elemente"
IDS_AUTHOR = "Entwickler"
IDS_COPYRIGHT = "Copyright"
IDS_PLUGIN_NO_OPTIONS_INFO= "Das Plugin hat keine Optionseinstellungen."
IDS_PLUGIN_NAME = "Plugin-Name"
IDS_DISABLED = "Deaktiviert"
IDS_RESTART_TO_APPLY_CHANGE_INFO = "Bitte starte das Programm neu um die Änderungen anzuwenden."
IDS_VERSION = "Version"
IDS_DISP_ITEM_ID = "Angezeigte Element-ID"
IDS_PLUGIN_API_VERSION = "API-Version"
IDS_WEEK_VIEW = "Wochenansicht"
IDS_WEEK_NUM = "Woche <%1%>"
IDS_URL = "Url"
IDS_PLUGIN_VERSION_NOT_SUPPORT= "Die Plugin-Version ist zu niedrig."
IDS_MODIFY_PRESET = "Voreinstellung ändern"
IDS_SELECT_AT_LEASE_ONE_WARNING= "Bitte mindestens eins auswählen!"
IDS_AUTO_SAVE_TO_PRESET_TIP = "Wenn die Funktion= ""Automatisch an Windows 10 Hell/Dunkel-Theme anpassen"" und diese Option aktiviert sind, werden die Farben des Taskbarfensters, wenn sie geändert werden, automatisch in die jeweilige Voreinstellung entsprechend dem aktuellen Windows-Hell-/Dunkel-Modus gespeichert."
IDS_TOTAL_NET_SPEED = "Gesamtgeschwindigkeit"
IDS_SHOW_RESOURCE_USAGE_GRAPH_TIP = "Ressourcennutzungsdiagramm anzeigen für CPU-/Speicher-/Festplattennutzung, Temperatur und Plugin-Elemente."
IDS_SHOW_NET_SPEED_GRAPH_TIP = "Netzgeschwindigkeitsdiagramm für Upload, Download und Gesamtgeschwindigkeit anzeigen."
IDS_REFRESH_CONNECTION_LIST= "Verbindungsliste aktualisieren"
IDS_HARDWARE_MONITOR_INIT_FAILED = "Die Initialisierung der Hardwareüberwachung ist fehlgeschlagen und deshalb nicht verfügbar!"
IDS_HARDWARE_INFO_ACQUIRE_FAILED_ERROR = "Fehler beim Erfassen der Hardwareüberwachungsdaten!"
IDS_AUTO_RUN_METHOD_REGESTRY= "Autostart-Modus : Registry"
IDS_AUTO_RUN_METHOD_TASK_SCHEDULE= "Autostart-Modus: Aufgabenplanung"
IDS_PATH = "Pfad"
IDS_SET_AUTO_RUN_FAILED_WARNING= "Aktivieren des Autostarts beim Gerätestart fehlgeschlagen!"
IDS_UPDATE_TASKBARDLG_FAILED_TIP = "Aktualisierung des Taskbarfensterinhalts fehlgeschlagen, Direct2Dd-Rendering wurde deaktiviert. Hinweis: Das Einstellungsfenster könnte die Einstellungsanpassung nicht sofort aktualisieren."
IDS_D2DDRAWCOMMON_ERROR_TIP = "Ein Fehler ist während des Direct2Dd-Rendering aufgetreten und es wurde deaktiviert. Hinweis: Das Einstellungsfenster könnte die Einstellungsanpassung nicht sofort aktualisieren."
IDS_PLUGIN_OPTIONS = "Plugin-Optionen"
IDS_GET_CPU_USAGE_BY_PDH_FAILED_LOG = "Das Erfassen der CPU-Nutzung durch den Performance Counter ist fehlgeschlagen, fullCounterPath=<%1%>"
IDS_PRIMARY_DISPLAY = "Primäres Display"
IDS_SECONDARY_DISPLAY = "Sekundäres Display <%1%>"
IDS_RESTORE_FROM_SLEEP_LOG = "Das System wurde aus dem Ruhezustand geweckt, die Verbindung wurde reinitialisiert. (<%1%> mal reinitialisiert.)"

TXT_OK = "OK"
TXT_CANCEL = "Abbrechen"
TXT_CLOSE = "Schließen"
TXT_APPLY = "Anwenden"

; Text used for dialog. (Must be started with "TXT_")
; CAboutDlg
TXT_TITLE_ABOUT = "Über TrafficMonitor"
TXT_ABOUT_VERSION = "TrafficMonitor<%1%>, V<%2%>"
TXT_ABOUT_COPYRIGHT = "Copyright (C) 2017-2025 By ZhongYang\nLast compiled date: <compile_date>"
TXT_ABOUT_TRANSLATOR = "<%1%> Übersetzer: <%2%>"
TXT_ABOUT_THIRD_PARTY_LIB = "In diesen Projekt verwendete Third-Party-Bibliotheken:"
TXT_ABOUT_AUTHOR_S_OTHER_SOFTWARE = "Andere Software des Entwicklers:"
TXT_ABOUT_CONTACT_AUTHOR = "Entwickler kontaktieren"
TXT_ABOUT_LICENSE = "Lizenz"
TXT_ABOUT_ACKNOWLEDGEMENT = "Anerkennung"
TXT_ABOUT_DONATE = "Spenden"

; CAppAlreadyRuningDlg
TXT_TRAFFICMONITOR_ALREAD_RUNING = "TrafficMonitor läuft bereits."
TXT_EXIT_THE_PROGRAM = "Das &Programm verlassen"
TXT_OPEN_OPTION_SETTINGS = "&Einstellungen öffnen"
TXT_SHOW_HIDE_MAIN_WINDOW = "&Hauptfenster anzeigen/verstecken"
TXT_SHOW_HIDE_TASKBAR_WINDOW = "&Taskbarfenster anzeigen/verstecken"

; CAutoAdaptSettingsDlg
TXT_TITLE_AUTO_ADATP_SETTINGS = "Einstellungen für Auto-Anpassung"
TXT_COLOR_PRESET_IN_DARK_MODE = "Farbvoreinstellung für den dunklen Windows-Modus:"
TXT_COLOR_PRESET_IN_LIGHT_MODE = "Farbvoreinstellung für den hellen Windows-Modus:"
TXT_AUTO_SAVE_TO_PRESET_CHECK = "Farbeinstellungen des Taskbarfenster automatisch in die Voreinstellungen sichern"

; CDisplayTextSettingDlg
TXT_TITLE_DISPLAY_TEXT_SETTING = "Anzeigetext-Einstellungen"
TXT_RESTORE_DEFAULT = "&Standard wiederherstellen"

; CDonateDlg
TXT_TITLE_DONATE = "Spenden"
TXT_DONATE_INFO = "Wenn du denkst, dass dieses Programm für dich hilfreich ist, kannst du den folgenden QR-Code durch Alipay oder WeChat Pay scannen um an den Entwickler zu spenden und ihm zu helfen das Programm besser zu machen. Bitte fühl dich frei, den Betrag nach deinem Ermessen zu wählen."

; CGeneralSettingsDlg
TXT_APPLICATION_SETTINGS = "Programmeinstellungen"
TXT_CHECK_UPDATE = "Beim Start auf Updates prüfen"
TXT_CHECK_NOW = "&Jetzt prüfen"
TXT_UPDATE_SOURCE = "Updatequelle:"
TXT_AUTO_RUN_CHECK = "Automatisch starten, wenn Windows startet"
TXT_RESET_AUTO_RUN_BUTTON = "Autorun zurücksetzen"
TXT_LANGUAGE = "Sprache:"
TXT_CONFIGURATION_AND_DATA_FILES = "Konfigurations- und Datendateien"
TXT_SAVE_TO_APPDATA_RADIO = "Ins Appdata-Verzeichnis sichern"
TXT_SAVE_TO_PROGRAM_DIR_RADIO = "Ins Programmverzeichnis sichern"
TXT_OPEN_CONFIG_PATH_BUTTON = "Konfigurationsdatei und -&Verzeichnis öffnen"
TXT_NOTIFICATION_MESSAGE = "Benachrichtigungstext"
TXT_TODAY_TRAFFIC_TIP_CHECK = "Benachrichtigen, wenn der heutige Verkehr erreicht wurde"
TXT_TODAY_TRAFFIC_BACK = " "
TXT_MEMORY_USAGE_TIP_CHECK = "Benachrichtigen, wenn die Speichernutzung erreicht wurde"
TXT_MEMORY_USAGE_BACK = "%"
TXT_CPU_TEMP_TIP_CHECK = "Benachrichtigen, wenn die CPU-Temperatur erreicht wurde"
TXT_CPU_TEMP_BACK = "°C"
TXT_GPU_TEMP_TIP_CHECK = "Benachrichtigen, wenn die GPU-Temperatur erreicht wurde"
TXT_GPU_TEMP_BACK = "°C"
TXT_HDD_TEMP_TIP_CHECK = "Benachrichtigen, wenn die Festplattentemperatur erreicht wurde"
TXT_HDD_TEMP_BACK = "°C"
TXT_MBD_TEMP_TIP_CHECK = "Benachrichtigen, wenn die Mainboardtemperatur erreicht wurde"
TXT_MBD_TEMP_BACK = "°C"
TXT_HARDWARE_MONITORING = "Hardwareüberwachung"
TXT_CPU = "CPU"
TXT_GPU = "GPU"
TXT_HARD_DISK = "Festplatte"
TXT_MAIN_BOARD = "Mainboard"
TXT_SELECT_HDD_STATIC = "Zu überwachende Festplatte auswählen:"
TXT_SELECT_CPU_STATIC = "Zu überwachende CPU-Temperatur auswählen:"
TXT_ADVANCED = "Erweitert"
TXT_SHOW_ALL_CONNECTION_CHECK = "Alle Netzwerkverbindungen anzeigen"
TXT_SELECT_CONNECTIONS_BUTTON = "Zu überwachende &Verbindungen auswählen..."
TXT_CPU_ACQUISITION_METHOD = "Erfassungsmethode für die CPU-Nutzung:"
TXT_USE_CPU_TIME_RADIO = "Auf Basis der CPU-Zeit"
TXT_USE_PDH_RADIO = "Den Performance Counter nutzen"
TXT_USE_HARDWARE_MONITOR_RADIO = "Den Hardwaremonitor nutzen"
TXT_MONITORING_INTERVALS = "Überwachungsintervalle:"
TXT_MILLISECONDS = "Millisekunden"
TXT_RESTORE_DEFAULT_TIME_SPAN_BUTTON = "&Standard wiederherstellen"
TXT_PLUGIN_MANAGE_BUTTON = "Plugin-Manager..."
TXT_DISPLAY = "Display"
TXT_SHOW_NOTIFY_ICON_CHECK = "&Benachrichtigungssymbol anzeigen"
TXT_SKINS = "Skins"
TXT_ALLOW_SKIN_FONT_CHECK = "Skins erlauben die Schrifteinstellungen zu überschreiben"
TXT_ALLOW_SKIN_DISP_STR_CHECK = "Skins erlauben die Anzeigetexteinstellungen zu überschreiben"

; CHistoryTrafficCalendarDlg
TXT_YEAR = "Jahr:"
TXT_MONTH = "Monat:"

; CHistoryTrafficDlg
TXT_TITLE_HISTORY_TRAFFIC = "Netzverkehrsstatistik"

; CHistoryTrafficListDlg
TXT_VIEW_TYPE = "Zeitraum:"
TXT_FIGURE_VIEW_SCALE = "Anzeigemaßstab:"

; CIconSelectDlg
TXT_TITLE_CHANGE_ICON = "Benachrichtigungssymbol ändern"
TXT_SELECT_A_ICON = "Symbol wählen:"
TXT_PREVIEW = "Vorschau"
TXT_NOTIFY_ICON_AUTO_ADAPT_CHECK = "Automatisch an Windows 10 Hell/Dunkel-Theme anpassen"

; CMainWndColorDlg
TXT_TITLE_MAIN_COLOR_DIALOG = "Hauptfenster Farbeinstellungen"

; CMainWndSettingsDlg
TXT_FULLSCREEN_HIDE_CHECK = "Hauptfenster verstecken, wenn das Programm im Vollbildmodus läuft"
TXT_COLOR_AND_FONT = "Farbe und Schrift"
TXT_FONT = "Schriftart:"
TXT_FONT_SIZE = "Schriftgröße:"
TXT_SET_FONT_BUTTON = "&Schriftart wählen..."
TXT_TEXT_COLOR = "Textfarbe:"
TXT_DISPLAY_TEXT = "Anzeigetext"
TXT_SWITCH_UP_DOWN_CHECK = "Upload- und Download-Position tauschen"
TXT_UNIT_SETTINGS = "Einstellungen Einheit"
TXT_UNIT_SELECTION = "Auswahl Einheit:"
TXT_HIDE_UNIT_CHECK = "Netzgeschwindigkeitseinheit nicht anzeigen"
TXT_SPEED_SHORT_MODE_CHECK = "Gekürzte Netzgeschwindigkeitsanzeige"
TXT_HIDE_PERCENTAGE_CHECK = "Prozentzeichen nicht anzeigen"
TXT_SPECIFY_EACH_ITEM_COLOR_CHECK = "Farben für jedes Element einstellen"
TXT_DOUBLE_CLICK_ACTION = "Doppelklick-Aktion:"
TXT_SEPARATE_VALUE_UNIT_CHECK = "Wert und Einheit durch Leerzeichen trennen"
TXT_NETSPEED_UNIT = "Netzgeschwindigkeitseinheit:"
TXT_UNIT_BYTE_RADIO = "B (Byte)"
TXT_UNIT_BIT_RADIO = "b (bit)"
TXT_SHOW_TOOL_TIP_CHK = "Maus-Tooltip anzeigen"
TXT_EXE_PATH_STATIC = "Spezifizierte Anwendung:"
TXT_BROWSE_BUTTON = "&Browsen..."
TXT_DISPLAY_TEXT_SETTING_BUTTON = "Anzeige&text-Einstellungen..."
TXT_MEMORY_DISPLAY_MODE = "Speicheranzeigemodus:"
TXT_ALWAYS_ON_TOP_CHECK = "Immer im Vordergrund"
TXT_MOUSE_PENETRATE_CHECK = "Durchklickbar"
TXT_LOCK_WINDOW_POS_CHECK = "Fensterposition sperren"
TXT_ALOW_OUT_OF_BORDER_CHECK = "Erlaube über Bildschirmrand hinaus"

; CMessageDlg
TXT_TITLE_MESSAGE_DLG = "Nachricht"

; CNetworkInfoDlg
TXT_TITLE_NETWORK_INFO_DLG = "Verbindungsdetails"

; COptionsDlg
TXT_TITLE_OPTION = "Einstellungen"

; CPluginManagerDlg
TXT_TITLE_PLUGIN_MANAGE = "Plugin-Manager"
TXT_OPTINS_BUTTON = "&Optionen..."
TXT_PLUGIN_INFO_BUTTON = "&Details..."
TXT_PLUGIN_DEV_GUID_STATIC = "Plugin-Entwicklungsleitfaden"
TXT_PLUGIN_DOWNLOAD_STATIC = "Mehr Plugins herunterladen"
TXT_OPEN_PLUGIN_DIR_STATIC = "Plugin-Verzeichnis öffnen"

;CSelectConnectionsDlg
TXT_TITLE_SELECTION_CONNECTION = "Zu überwachende Verbindung auswählen"

; CSetItemOrderDlg
TXT_TITLE_SELECT_ORDER_DIALOG = "Anzeigeeinstellungen"
TXT_MOVE_UP_BUTTON = "Hoch"
TXT_MOVE_DOWN_BUTTON = "Runter"
TXT_RESTORE_DEFAULT_BUTTON = "&Standard wiederherstellen"

; CSkinAutoAdaptSettingDlg
TXT_TITLE_SKIN_AUTO_ADAPTT_DLG = "Einstellungen für automatische Skin-Umschaltung"
TXT_SKIN_IN_DARK_MODE = "Skin im dunklen Modus von Windows:"
TXT_SKIN_IN_LIGHT_MODE = "Skin im hellen Modus von Windows:"

; CSkinDlg
TXT_TITLE_SKIN_DLG = "Skin wechseln"
TXT_PREVIEW_GROUP_STATIC = "Vorschau"
TXT_SELECT_A_SKIN = "Skin wählen"
TXT_SKIN_AUTHOR = "Skin-Autor:"
TXT_SKIN_MAKING_UP_TUTORIAL = "Anleitung für Skin-Erstellung"
TXT_DOWNLOAD_MORE_SKIN = "Weitere Skins herunterladen"
TXT_OPEN_SKIN_DIR = "Skin-Verzeichnis öffnen"
TXT_SKIN_AUTO_ADAPT_CHECK = "Skins automatisch entsprechend Windows-Dunkel-/Hell-Modus umschalten."
TXT_SKIN_AUTO_ADAPT_BUTTON = "Einstellungen für Auto-Umschaltung..."

; CTaskbarColorDlg
TXT_TITLE_TASKBAR_COLOR_DLG = "Taskbarfenster-Farbeinstellungen"

; CTaskBarSettingsDlg
TXT_PRESET = "&Voreinstellung"
TXT_BACKGROUND_COLOR = "Hintergrundfarbe:"
TXT_BACKGROUND_TRANSPARENT = "Hintergrund transparent"
TXT_AUTO_SET_BACK_COLOR = "Hintergrund entsprechend der Taskbarfarbe automatisch einstellen"
TXT_AUTO_ADAPT_LIGHT_THEME = "Automatisch an Windows 10 Hell/Dunkel-Theme anpassen"
TXT_AUTO_ADAPT_SETTINGS_BUTTON = "&Einstellungen für Auto-Anpassung..."
TXT_DISPLAY_SETTINGS = "Anzeigeeinstellungen"
TXT_DISPLAY_SETTINGS_BUTTON = "&Anzeigeeinstellungen..."
TXT_SPEED_SHORT_MODE = "Gekürzte Netzgeschwindigkeitsanzeige"
TXT_VALUE_RIGHT_ALIGN = "Werte rechts ausrichten"
TXT_NET_SPEED_DATA_WIDTH = "Breite der Netzgeschwindigkeitsanzeige:"
TXT_CHARACTORS = "Zeichen"
TXT_HORIZONTAL_ARRANGE = "Horizontal anzeigen"
TXT_ITEM_SPACING = "Abstand zwischen Elementen:"
TXT_VERTICAL_MARGIN = "Vertikaler Rand:"
TXT_PIXELS = "Pixel"
TXT_TASKBAR_WINDOW = "Taskbarfenster"
TXT_TASKBAR_WND_ON_LEFT = "Taskbarfenster erscheint auf der Taskbar links"
TXT_DISPLAY_TO_SHOW_TASKBAR_WND = "Display, auf dem das Taskbarfenster angezeigt wird:"
TXT_WIN11_SETTINGS_BUTTON = "Einstellungen entsprechend Windows 11"
TXT_RESOURCE_USAGE_GRAPH = "Ressourcennutzungsdiagramm"
TXT_SHOW_RESOURCE_USAGE_GRAPH = "Ressourcennutzungsdiagramm anzeigen"
TXT_SHOW_DASHED_BOX = "Gestrichelte Umrandung anzeigen"
TXT_SHOW_NET_SPEED_GRAPH = "Netzgeschwindigkeitsdiagramm anzeigen"
TXT_NET_SPEED_GRAPH_MAX_VALUE = "Maximalwert der Netzgeschwindigkeitsdiagramm:"
TXT_USAGE_GRAPH_COLOR = "Nutzungsdiagrammfarbe:"
TXT_GRAPH_DISPLAY_MODE = "Diagrammanzeige:"
TXT_BAR_MODE = "Balken-Modus"
TXT_PLOT_MODE = "Plot-Modus"
TXT_RENDERING_SETTINGS = "Rendering-Einstellungen"
TXT_RENDERING_SETTINGS_NOTE = "Hinweis:Direct2D-Rendering wird nur genutzt, wenn ""Background transparent"" aktiviert und ""Hintergrund entsprechend der Taskbarfarbe automatisch einstellen"" nicht aktiviert ist."
TXT_ENABLE_COLOR_EMOJI = "Farbige Emoji aktivieren"

; CWin11TaskbarSettingDlg
TXT_TITLE_WIN11_TASKBAR_SETTING = "Einstellungen entsprechend Windows 11"
TXT_TASKBAR_WINDOWS_CLOSE_TO_ICON = "Taskbarfenster beim Symbol statt an den Seiten der Taskbar"
TXT_WINDOW_VERTICAL_OFFSET = "Vertikaler Versatz des Fensters:"
TXT_WINDOW_HORIZONTAL_OFFSET = "Horizontaler Versatz des Fensters:"
TXT_AVOID_OVERLAP_RIGHT_WIDGETS_CHECK = "Überlagerung mit den rechten Widgets vermeiden (Auswählen, wenn die Widgets auf der rechten Seite der Taskbar erscheinen)"
TXT_WIDGETS_WIDTH = "Widgetbreite:"

[menu]
; IDR_HISTORY_TRAFFIC_MENU
TXT_SHOW_SCALE = "Maßstab anzeigen"
TXT_USE_LINEAR_SCALE = "Linearen Maßstab nutzen"
TXT_USE_LOG_SCALE = "Logarithmischen Maßstab nutzen"
TXT_FIRST_DAT_OF_WEEEK = "Erster Tag der Woche"
TXT_SUNDAY = "Sonntag"
TXT_MONDAY = "Montag"
TXT_GO_TO_TODAY = "Gehe zu heute"

; IDR_INFO_MENU
TXT_COPY_TEXT = "&Text kopieren"

; IDR_MENU1
TXT_SELECT_CONNECTIONS = "&Netzwerkverbindungen wählen"
TXT_AUTO_SELECT = "Aut&omatisch auswählen"
TXT_SELECT_ALL = "Alle aus&wählen"
TXT_CONNECTION_DETAILS = "Verbindungs&details"
TXT_ALWAYS_ON_TOP = "Immer im &Vordergrund"
TXT_MOUSE_PENETRATE = "Durch&klickbar"
TXT_LOCK_WINDOW_POS = "&Fensterposition sperren"
TXT_SHOW_MORE_INFO = "Mehr &Infos anzeigen"
TXT_SHOW_TASKBAR_WINDOW = "&Taskbarfenster anzeigen"
TXT_SHOW_MAIN_WINDOW = "&Hauptfenster anzeigen"
TXT_WINDOW_OPACITY = "F&ensterundurchsichtigkeit "
TXT_OTHER_FUNCTIONS = "Andere F&unktionen"
TXT_CHANGE_SKIN = "&Skin wechseln..."
TXT_CHANGE_NOTIFY_ICON = "Benach&richtigungssymbol wechseln..."
TXT_ALLOW_OUT_OF_BOUNDARIES = "Erlaube über &Bildschirmrand hinaus"
TXT_HISTORY_TRAFFIC_STATISTICS = "&Netzverkehrsstatistik"
TXT_PLUGIN_MANAGE = "&Plugin-Manager..."
TXT_OPTIONS = "&Optionen..."
TXT_HELP_MENU = "H&ilfe..."
TXT_HELP = "&Hilfe"
TXT_FAQ = "Häufige Fragen (FA&Q)"
TXT_UPDATE_LOG = "Update-&Log"
TXT_ABOUT = "Übe&r..."
TXT_CHECK_UPDATE = "&Auf Updates prüfen..."
TXT_EXIT = "E&xit"

; IDR_TASK_BAR_MENU
TXT_DISPLAY_SETTINGS = "&Anzeigeeinstellungen..."
TXT_CLOSE_TASKBAR_WINDOW = "Taskbarfenster &schließen"
TXT_TASK_MANAGER = "&Taskmanager"

;IDR_PLUGIN_MANAGER_MENU
TXT_PLUGIN_DETAIL = "&Details..."
TXT_PLUGIN_OPTIONS = "&Optionen..."
TXT_PLUGIN_DISABLE = "De&aktiviert"
