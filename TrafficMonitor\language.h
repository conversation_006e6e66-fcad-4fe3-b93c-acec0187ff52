﻿#pragma once 
 
#define IDS_CHECK_UPDATE_FAILD L"IDS_CHECK_UPDATE_FAILD"
#define IDS_CHECK_UPDATE_ERROR L"IDS_CHECK_UPDATE_ERROR"
#define IDS_UPDATE_AVLIABLE L"IDS_UPDATE_AVLIABLE"
#define IDS_UPDATE_AVLIABLE2 L"IDS_UPDATE_AVLIABLE2"
#define IDS_ALREADY_UPDATED L"IDS_ALREADY_UPDATED"
#define IDS_AUTORUN_FAILED_NO_KEY L"IDS_AUTORUN_FAILED_NO_KEY"
#define IDS_AUTORUN_FAILED_NO_ACCESS L"IDS_AUTORUN_FAILED_NO_ACCESS"
#define IDS_AUTORUN_DELETE_FAILED L"IDS_AUTORUN_DELETE_FAILED"
#define IDS_AN_INSTANCE_RUNNING L"IDS_AN_INSTANCE_RUNNING"
#define IDS_TRAFFIC_USED_TODAY L"IDS_TRAFFIC_USED_TODAY"
#define IDS_MEMORY_USAGE L"IDS_MEMORY_USAGE"
#define IDS_CPU_USAGE L"IDS_CPU_USAGE"
#define IDS_SEND_EMAIL_TO_ATHOUR L"IDS_SEND_EMAIL_TO_ATHOUR"
#define IDS_GOTO_GITHUB L"IDS_GOTO_GITHUB"
#define IDS_DONATE_ATHOUR L"IDS_DONATE_ATHOUR"
#define IDS_UPLOAD L"IDS_UPLOAD"
#define IDS_DOWNLOAD L"IDS_DOWNLOAD"
#define IDS_MEMORY L"IDS_MEMORY"
#define IDS_UPLOAD_DISP L"IDS_UPLOAD_DISP"
#define IDS_DOWNLOAD_DISP L"IDS_DOWNLOAD_DISP"
#define IDS_MEMORY_DISP L"IDS_MEMORY_DISP"
#define IDS_CONNOT_SAVE_CONFIG_WARNING L"IDS_CONNOT_SAVE_CONFIG_WARNING"
#define IDS_TRAFFICMONITOR L"IDS_TRAFFICMONITOR"
#define IDS_INSUFFICIENT_BUFFER L"IDS_INSUFFICIENT_BUFFER"
#define IDS_CONNECTION_NUM_CHANGED L"IDS_CONNECTION_NUM_CHANGED"
#define IDS_CONNECTION_NOT_MATCH L"IDS_CONNECTION_NOT_MATCH"
#define IDS_CONNOT_INSERT_TO_TASKBAR L"IDS_CONNOT_INSERT_TO_TASKBAR"
#define IDS_MEMORY_UDAGE_EXCEED L"IDS_MEMORY_UDAGE_EXCEED"
#define IDS_NOTIFY L"IDS_NOTIFY"
#define IDS_TODAY_TRAFFIC_EXCEED L"IDS_TODAY_TRAFFIC_EXCEED"
#define IDS_DATE L"IDS_DATE"
#define IDS_TRAFFIC_USED L"IDS_TRAFFIC_USED"
#define IDS_FIGURE L"IDS_FIGURE"
#define IDS_DEFAULT_ICON L"IDS_DEFAULT_ICON"
#define IDS_ICON L"IDS_ICON"
#define IDS_INTERFACE_NAME L"IDS_INTERFACE_NAME"
#define IDS_INTERFACE_DESCRIPTION L"IDS_INTERFACE_DESCRIPTION"
#define IDS_CONNECTION_TYPE L"IDS_CONNECTION_TYPE"
#define IDS_IF_TYPE_OTHER L"IDS_IF_TYPE_OTHER"
#define IDS_IF_TYPE_ETHERNET_CSMACD L"IDS_IF_TYPE_ETHERNET_CSMACD"
#define IDS_IF_TYPE_ISO88025_TOKENRING L"IDS_IF_TYPE_ISO88025_TOKENRING"
#define IDS_IF_TYPE_FDDI L"IDS_IF_TYPE_FDDI"
#define IDS_IF_TYPE_PPP L"IDS_IF_TYPE_PPP"
#define IDS_IF_TYPE_SOFTWARE_LOOPBACK L"IDS_IF_TYPE_SOFTWARE_LOOPBACK"
#define IDS_IF_TYPE_ATM L"IDS_IF_TYPE_ATM"
#define IDS_IF_TYPE_IEEE80211 L"IDS_IF_TYPE_IEEE80211"
#define IDS_IF_TYPE_TUNNEL L"IDS_IF_TYPE_TUNNEL"
#define IDS_IF_TYPE_IEEE1394 L"IDS_IF_TYPE_IEEE1394"
#define IDS_IF_TYPE_IEEE80216_WMAN L"IDS_IF_TYPE_IEEE80216_WMAN"
#define IDS_IF_TYPE_WWANPP L"IDS_IF_TYPE_WWANPP"
#define IDS_IF_TYPE_WWANPP2 L"IDS_IF_TYPE_WWANPP2"
#define IDS_UNKNOW_CONNECTION L"IDS_UNKNOW_CONNECTION"
#define IDS_SPEED L"IDS_SPEED"
#define IDS_ADAPTER_PHYSICAL_ADDRESS L"IDS_ADAPTER_PHYSICAL_ADDRESS"
#define IDS_IP_ADDRESS L"IDS_IP_ADDRESS"
#define IDS_SUBNET_MASK L"IDS_SUBNET_MASK"
#define IDS_DEFAULT_GATEWAY L"IDS_DEFAULT_GATEWAY"
#define IDS_OPERATIONAL_STATUS L"IDS_OPERATIONAL_STATUS"
#define IDS_IF_OPER_STATUS_NON_OPERATIONAL L"IDS_IF_OPER_STATUS_NON_OPERATIONAL"
#define IDS_IF_OPER_STATUS_UNREACHABLE L"IDS_IF_OPER_STATUS_UNREACHABLE"
#define IDS_IF_OPER_STATUS_DISCONNECTED L"IDS_IF_OPER_STATUS_DISCONNECTED"
#define IDS_IF_OPER_STATUS_CONNECTING L"IDS_IF_OPER_STATUS_CONNECTING"
#define IDS_IF_OPER_STATUS_CONNECTED L"IDS_IF_OPER_STATUS_CONNECTED"
#define IDS_IF_OPER_STATUS_OPERATIONAL L"IDS_IF_OPER_STATUS_OPERATIONAL"
#define IDS_UNKNOW_STATUS L"IDS_UNKNOW_STATUS"
#define IDS_BYTES_RECEIVED L"IDS_BYTES_RECEIVED"
#define IDS_BYTES_SENT L"IDS_BYTES_SENT"
#define IDS_BYTES_RECEIVED_SINCE_START L"IDS_BYTES_RECEIVED_SINCE_START"
#define IDS_BYTES_SENT_SINCE_START L"IDS_BYTES_SENT_SINCE_START"
#define IDS_PROGRAM_ELAPSED_TIME L"IDS_PROGRAM_ELAPSED_TIME"
#define IDS_HOUR_MINUTE_SECOND L"IDS_HOUR_MINUTE_SECOND"
#define IDS_INTERNET_IP_ADDRESS L"IDS_INTERNET_IP_ADDRESS"
#define IDS_GET_FAILED L"IDS_GET_FAILED"
#define IDS_ITEM L"IDS_ITEM"
#define IDS_VALUE L"IDS_VALUE"
#define IDS_COPY_TO_CLIPBOARD_FAILED L"IDS_COPY_TO_CLIPBOARD_FAILED"
#define IDS_SKIN_AUTHOUR L"IDS_SKIN_AUTHOUR"
#define IDS_OVERWRITE_FONT_TEXT_WARNING L"IDS_OVERWRITE_FONT_TEXT_WARNING"
#define IDS_OVERWRITE_FONT_WARNING L"IDS_OVERWRITE_FONT_WARNING"
#define IDS_OVERWRITE_TEXT_WARNING L"IDS_OVERWRITE_TEXT_WARNING"
#define IDS_SPEED_SHORT_MODE_TIP L"IDS_SPEED_SHORT_MODE_TIP"
#define IDS_AUTO L"IDS_AUTO"
#define IDS_FIXED_AS L"IDS_FIXED_AS"
#define IDS_OPEN_CONNECTION_DETIAL L"IDS_OPEN_CONNECTION_DETIAL"
#define IDS_OPEN_HISTORICAL_TRAFFIC L"IDS_OPEN_HISTORICAL_TRAFFIC"
#define IDS_SHOW_HIDE_MORE_INFO L"IDS_SHOW_HIDE_MORE_INFO"
#define IDS_SHOW_HIDE_CPU_MEMORY L"IDS_SHOW_HIDE_CPU_MEMORY"
#define IDS_OPEN_OPTION_SETTINGS L"IDS_OPEN_OPTION_SETTINGS"
#define IDS_OPEN_TASK_MANAGER L"IDS_OPEN_TASK_MANAGER"
#define IDS_CHANGE_SKIN L"IDS_CHANGE_SKIN"
#define IDS_NONE L"IDS_NONE"
#define IDS_FONT_SIZE_WARNING L"IDS_FONT_SIZE_WARNING"
#define IDS_SAME_TEXT_BACK_COLOR_WARNING L"IDS_SAME_TEXT_BACK_COLOR_WARNING"
#define IDS_SAME_BACK_TEXT_COLOR_WARNING L"IDS_SAME_BACK_TEXT_COLOR_WARNING"
#define IDS_FOLLOWING_SYSTEM L"IDS_FOLLOWING_SYSTEM"
#define IDS_LANGUAGE_CHANGE_INFO L"IDS_LANGUAGE_CHANGE_INFO"
#define IDS_MAIN_WINDOW_SETTINGS L"IDS_MAIN_WINDOW_SETTINGS"
#define IDS_TASKBAR_WINDOW_SETTINGS L"IDS_TASKBAR_WINDOW_SETTINGS"
#define IDS_GENERAL_SETTINGS L"IDS_GENERAL_SETTINGS"
#define IDS_ACQUIRING L"IDS_ACQUIRING"
#define IDS_LIST_VIEW L"IDS_LIST_VIEW"
#define IDS_CALENDAR_VIEW L"IDS_CALENDAR_VIEW"
#define IDS_MONDAY L"IDS_MONDAY"
#define IDS_TUESDAY L"IDS_TUESDAY"
#define IDS_WEDNESDAY L"IDS_WEDNESDAY"
#define IDS_THURSDAY L"IDS_THURSDAY"
#define IDS_FRIDAY L"IDS_FRIDAY"
#define IDS_SATURDAY L"IDS_SATURDAY"
#define IDS_SUNDAY L"IDS_SUNDAY"
#define IDS_CURRENT_MONTH_TOTAL_TRAFFIC L"IDS_CURRENT_MONTH_TOTAL_TRAFFIC"
#define IDS_TRAFFIC_USED1 L"IDS_TRAFFIC_USED1"
#define IDS_CONNOT_INSERT_TO_TASKBAR_ERROR_LOG L"IDS_CONNOT_INSERT_TO_TASKBAR_ERROR_LOG"
#define IDS_NO_CONNECTION L"IDS_NO_CONNECTION"
#define IDS_CONTACT_TRANSLATOR L"IDS_CONTACT_TRANSLATOR"
#define IDS_THANKS_DONORS L"IDS_THANKS_DONORS"
#define IDS_GET_URL_ERROR_LOG_INFO L"IDS_GET_URL_ERROR_LOG_INFO"
#define IDS_SHOW_ALL_INFO_TIP L"IDS_SHOW_ALL_INFO_TIP"
#define IDS_CFG_DIR_CHANGED_INFO L"IDS_CFG_DIR_CHANGED_INFO"
#define IDS_DOUBLE_CLICK_TO_ACQUIRE L"IDS_DOUBLE_CLICK_TO_ACQUIRE"
#define IDS_ERROR1 L"IDS_ERROR1"
#define IDS_ERROR_MESSAGE L"IDS_ERROR_MESSAGE"
#define IDS_CRASH_INFO L"IDS_CRASH_INFO"
#define IDS_TITLE_ACKNOWLEDGEMENT L"IDS_TITLE_ACKNOWLEDGEMENT"
#define IDS_SAVE_DEFAULT_STYLE_INQUIRY L"IDS_SAVE_DEFAULT_STYLE_INQUIRY"
#define IDS_SPECIFIC_APP L"IDS_SPECIFIC_APP"
#define IDS_EXE_FILTER L"IDS_EXE_FILTER"
#define IDS_PRESET L"IDS_PRESET"
#define IDS_LIGHT_MODE L"IDS_LIGHT_MODE"
#define IDS_AUTO_ADAPT_TIP_INFO L"IDS_AUTO_ADAPT_TIP_INFO"
#define IDS_WITHOUT_TEMPERATURE L"IDS_WITHOUT_TEMPERATURE"
#define IDS_MOUSE_PENETRATE_TIP_INFO L"IDS_MOUSE_PENETRATE_TIP_INFO"
#define IDS_HISTORY_TRAFFIC_LOST_ERROR_LOG L"IDS_HISTORY_TRAFFIC_LOST_ERROR_LOG"
#define IDS_LEGEND L"IDS_LEGEND"
#define IDS_LICENSE_EXPLAIN L"IDS_LICENSE_EXPLAIN"
#define IDS_LICENSE L"IDS_LICENSE"
#define IDS_DAY_VIEW L"IDS_DAY_VIEW"
#define IDS_MONTH_VIEW L"IDS_MONTH_VIEW"
#define IDS_QUARTER_VIEW L"IDS_QUARTER_VIEW"
#define IDS_YEAR_VIEW L"IDS_YEAR_VIEW"
#define IDS_LINEAR_SCALE L"IDS_LINEAR_SCALE"
#define IDS_LOG_SCALE L"IDS_LOG_SCALE"
#define IDS_CPU_TEMPERATURE L"IDS_CPU_TEMPERATURE"
#define IDS_GPU_TEMPERATURE L"IDS_GPU_TEMPERATURE"
#define IDS_CPU_FREQ L"IDS_CPU_FREQ"
#define IDS_HDD_TEMPERATURE L"IDS_HDD_TEMPERATURE"
#define IDS_MAINBOARD_TEMPERATURE L"IDS_MAINBOARD_TEMPERATURE"
#define IDS_GPU_DISP L"IDS_GPU_DISP"
#define IDS_HDD_DISP L"IDS_HDD_DISP"
#define IDS_MAINBOARD_DISP L"IDS_MAINBOARD_DISP"
#define IDS_CPU_TEMPERATURE_EXCEED L"IDS_CPU_TEMPERATURE_EXCEED"
#define IDS_GPU_TEMPERATURE_EXCEED L"IDS_GPU_TEMPERATURE_EXCEED"
#define IDS_HDD_TEMPERATURE_EXCEED L"IDS_HDD_TEMPERATURE_EXCEED"
#define IDS_MBD_TEMPERATURE_EXCEED L"IDS_MBD_TEMPERATURE_EXCEED"
#define IDS_MUSICPLAYER2_DESCRIPTION L"IDS_MUSICPLAYER2_DESCRIPTION"
#define IDS_SIMPLENOTEPAD_DESCRIPTION L"IDS_SIMPLENOTEPAD_DESCRIPTION"
#define IDS_COLOR L"IDS_COLOR"
#define IDS_COLOR_LABEL L"IDS_COLOR_LABEL"
#define IDS_COLOR_VALUE L"IDS_COLOR_VALUE"
#define IDS_GPU_USAGE L"IDS_GPU_USAGE"
#define IDS_IF_OPER_STATUS_UP L"IDS_IF_OPER_STATUS_UP"
#define IDS_IF_OPER_STATUS_DOWN L"IDS_IF_OPER_STATUS_DOWN"
#define IDS_IF_OPER_STATUS_DORMANT L"IDS_IF_OPER_STATUS_DORMANT"
#define IDS_GOTO_GITEE L"IDS_GOTO_GITEE"
#define IDS_USAGE_PERCENTAGE L"IDS_USAGE_PERCENTAGE"
#define IDS_MEMORY_USED L"IDS_MEMORY_USED"
#define IDS_MEMORY_AVAILABLE L"IDS_MEMORY_AVAILABLE"
#define IDS_DOTNET_NOT_INSTALLED_TIP L"IDS_DOTNET_NOT_INSTALLED_TIP"
#define IDS_VERSION_UPDATE L"IDS_VERSION_UPDATE"
#define IDS_AVREAGE_TEMPERATURE L"IDS_AVREAGE_TEMPERATURE"
#define IDS_HARDWARE_MONITOR_WARNING L"IDS_HARDWARE_MONITOR_WARNING"
#define IDS_HDD_USAGE L"IDS_HDD_USAGE"
#define IDS_FILE_NAME L"IDS_FILE_NAME"
#define IDS_STATUS L"IDS_STATUS"
#define IDS_PLUGIN_LOAD_SUCCEED L"IDS_PLUGIN_LOAD_SUCCEED"
#define IDS_PLUGIN_MODULE_LOAD_FAILED L"IDS_PLUGIN_MODULE_LOAD_FAILED"
#define IDS_PLUGIN_FUNCTION_GET_FAILED L"IDS_PLUGIN_FUNCTION_GET_FAILED"
#define IDS_PLUGIN_INFO L"IDS_PLUGIN_INFO"
#define IDS_NAME L"IDS_NAME"
#define IDS_DESCRIPTION L"IDS_DESCRIPTION"
#define IDS_FILE_PATH L"IDS_FILE_PATH"
#define IDS_ITEM_NUM L"IDS_ITEM_NUM"
#define IDS_ITEM_NAMES L"IDS_ITEM_NAMES"
#define IDS_AUTHOR L"IDS_AUTHOR"
#define IDS_COPYRIGHT L"IDS_COPYRIGHT"
#define IDS_PLUGIN_NO_OPTIONS_INFO L"IDS_PLUGIN_NO_OPTIONS_INFO"
#define IDS_PLUGIN_NAME L"IDS_PLUGIN_NAME"
#define IDS_DISABLED L"IDS_DISABLED"
#define IDS_RESTART_TO_APPLY_CHANGE_INFO L"IDS_RESTART_TO_APPLY_CHANGE_INFO"
#define IDS_VERSION L"IDS_VERSION"
#define IDS_DISP_ITEM_ID L"IDS_DISP_ITEM_ID"
#define IDS_PLUGIN_API_VERSION L"IDS_PLUGIN_API_VERSION"
#define IDS_WEEK_VIEW L"IDS_WEEK_VIEW"
#define IDS_WEEK_NUM L"IDS_WEEK_NUM"
#define IDS_URL L"IDS_URL"
#define IDS_PLUGIN_VERSION_NOT_SUPPORT L"IDS_PLUGIN_VERSION_NOT_SUPPORT"
#define IDS_MODIFY_PRESET L"IDS_MODIFY_PRESET"
#define IDS_SELECT_AT_LEASE_ONE_WARNING L"IDS_SELECT_AT_LEASE_ONE_WARNING"
#define IDS_AUTO_SAVE_TO_PRESET_TIP L"IDS_AUTO_SAVE_TO_PRESET_TIP"
#define IDS_TOTAL_NET_SPEED L"IDS_TOTAL_NET_SPEED"
#define IDS_SHOW_RESOURCE_USAGE_GRAPH_TIP L"IDS_SHOW_RESOURCE_USAGE_GRAPH_TIP"
#define IDS_SHOW_NET_SPEED_GRAPH_TIP L"IDS_SHOW_NET_SPEED_GRAPH_TIP"
#define IDS_REFRESH_CONNECTION_LIST L"IDS_REFRESH_CONNECTION_LIST"
#define IDS_HARDWARE_MONITOR_INIT_FAILED L"IDS_HARDWARE_MONITOR_INIT_FAILED"
#define IDS_HARDWARE_INFO_ACQUIRE_FAILED_ERROR L"IDS_HARDWARE_INFO_ACQUIRE_FAILED_ERROR"
#define IDS_AUTO_RUN_METHOD_REGESTRY L"IDS_AUTO_RUN_METHOD_REGESTRY"
#define IDS_AUTO_RUN_METHOD_TASK_SCHEDULE L"IDS_AUTO_RUN_METHOD_TASK_SCHEDULE"
#define IDS_PATH L"IDS_PATH"
#define IDS_SET_AUTO_RUN_FAILED_WARNING L"IDS_SET_AUTO_RUN_FAILED_WARNING"
#define IDS_UPDATE_TASKBARDLG_FAILED_TIP L"IDS_UPDATE_TASKBARDLG_FAILED_TIP"
#define IDS_D2DDRAWCOMMON_ERROR_TIP L"IDS_D2DDRAWCOMMON_ERROR_TIP"
#define IDS_PLUGIN_OPTIONS L"IDS_PLUGIN_OPTIONS"
#define IDS_GET_CPU_USAGE_BY_PDH_FAILED_LOG L"IDS_GET_CPU_USAGE_BY_PDH_FAILED_LOG"
#define IDS_PRIMARY_DISPLAY L"IDS_PRIMARY_DISPLAY"
#define IDS_SECONDARY_DISPLAY L"IDS_SECONDARY_DISPLAY"
#define IDS_RESTORE_FROM_SLEEP_LOG L"IDS_RESTORE_FROM_SLEEP_LOG"
#define IDS_PLUGIN_NEW_VERSION_INFO L"IDS_PLUGIN_NEW_VERSION_INFO"
#define IDS_TRAFFICMONITOR_PLUGIN_NITIFICATION L"IDS_TRAFFICMONITOR_PLUGIN_NITIFICATION"

#define TXT_OK L"TXT_OK"
#define TXT_CANCEL L"TXT_CANCEL"
#define TXT_CLOSE L"TXT_CLOSE"