﻿[general]
BCP_47 = "zh-CN"
DISPLAY_NAME = "简体中文"
TRANSLATOR = ""
TRANSLATOR_URL = ""
DEFAULT_FONT = "微软雅黑"

[text]
; String Table
IDS_CHECK_UPDATE_FAILD = "检查更新失败，请检查你的网络连接！"
IDS_CHECK_UPDATE_ERROR = "检查更新失败，从远程更新文件获取到了错误的信息，请联系作者！"
IDS_UPDATE_AVLIABLE = "检测到新版本 V%s，是否前往更新？"
IDS_UPDATE_AVLIABLE2 = "检测到新版本 V%s，更新内容：\r\n%s\r\n是否前往更新？"
IDS_ALREADY_UPDATED = "当前已经是最新版本。"
IDS_AUTORUN_FAILED_NO_KEY= "无法实现开机自启动，在注册表中找不到相应的键值！"
IDS_AUTORUN_FAILED_NO_ACCESS= "注册表项写入失败，可能该键值没有权限访问！"
IDS_AUTORUN_DELETE_FAILED= "注册表项删除失败，可能该键值没有权限访问！"
IDS_AN_INSTANCE_RUNNING= "已经有一个程序正在运行。"
IDS_TRAFFIC_USED_TODAY = "今日已使用流量"
IDS_MEMORY_USAGE = "内存利用率"
IDS_CPU_USAGE = "CPU利用率"
IDS_SEND_EMAIL_TO_ATHOUR= "向作者发送电子邮件"
IDS_GOTO_GITHUB = "转到此项目在GitHub上的页面"
IDS_DONATE_ATHOUR = "捐助作者"
IDS_UPLOAD = "上传"
IDS_DOWNLOAD = "下载"
IDS_MEMORY = "内存"
IDS_UPLOAD_DISP = "上传"
IDS_DOWNLOAD_DISP = "下载"
IDS_MEMORY_DISP = "内存"
IDS_CONNOT_SAVE_CONFIG_WARNING = "警告：无法保存设置，无法向文件 “<%file_path%>” 写入数据！以管理员身份运行程序可能会解决问题。"
IDS_TRAFFICMONITOR = "流量监控器"
IDS_INSUFFICIENT_BUFFER= "用于储存连接信息的缓冲区大小不够，已重新初始化连接。(已重新初始化<%cnt%>次)"
IDS_CONNECTION_NUM_CHANGED = "检测到连接数发生变化，已重新获取连接。先前连接数：<%before%>，现在连接数：<%after%>。(已重新初始化<%cnt%>次)"
IDS_CONNECTION_NOT_MATCH = "可能出现了异常，当前选择的连接和期望的连接不一致，已重新获取连接。(已重新初始化<%cnt%>次)"
IDS_CONNOT_INSERT_TO_TASKBAR = "窗口没有成功嵌入任务栏，可能已被安全软件阻止或者开始菜单未关闭，请尝试重启资源管理器。TrafficMonitor将继续尝试但不再提醒，错误代码："
IDS_MEMORY_UDAGE_EXCEED= "内存使用率已达到"
IDS_NOTIFY = "通知"
IDS_TODAY_TRAFFIC_EXCEED= "今日已经使用流量已达到"
IDS_DATE = "日期"
IDS_TRAFFIC_USED = "总流量"
IDS_FIGURE = "图表"
IDS_DEFAULT_ICON = "默认图标"
IDS_ICON = "图标"
IDS_INTERFACE_NAME = "接口名"
IDS_INTERFACE_DESCRIPTION= "接口描述"
IDS_CONNECTION_TYPE = "连接类型"
IDS_IF_TYPE_OTHER = "其他类型网络"
IDS_IF_TYPE_ETHERNET_CSMACD= "以太网网络"
IDS_IF_TYPE_ISO88025_TOKENRING= "令牌环网络"
IDS_IF_TYPE_FDDI = "光纤分布式数据接口 (FDDI) 网络"
IDS_IF_TYPE_PPP = "PPP 网络"
IDS_IF_TYPE_SOFTWARE_LOOPBACK= "软件环回网络"
IDS_IF_TYPE_ATM = "ATM 网络"
IDS_IF_TYPE_IEEE80211 = "IEEE 802.11 无线网络"
IDS_IF_TYPE_TUNNEL = "隧道类型封装网络"
IDS_IF_TYPE_IEEE1394 = "IEEE 1394 (Firewire) 高性能串行总线网络"
IDS_IF_TYPE_IEEE80216_WMAN= "对于 WiMax 设备的移动宽带"
IDS_IF_TYPE_WWANPP = "基于 GSM 网络设备的移动宽带"
IDS_IF_TYPE_WWANPP2 = "基于 CDMA 的设备移动宽带"
IDS_UNKNOW_CONNECTION = "未知网络"
IDS_SPEED = "速度"
IDS_ADAPTER_PHYSICAL_ADDRESS= "适配器物理地址"
IDS_IP_ADDRESS = "IP地址"
IDS_SUBNET_MASK = "子网掩码"
IDS_DEFAULT_GATEWAY = "默认网关"
IDS_OPERATIONAL_STATUS = "连接状态"
IDS_IF_OPER_STATUS_NON_OPERATIONAL= "LAN 适配器已被禁用"
IDS_IF_OPER_STATUS_UNREACHABLE= "WAN 适配器未连接"
IDS_IF_OPER_STATUS_DISCONNECTED= "网络电缆断开连接或无载体"
IDS_IF_OPER_STATUS_CONNECTING= "WAN 适配器正在连接"
IDS_IF_OPER_STATUS_CONNECTED= "WAN 适配器连接到远程对等方"
IDS_IF_OPER_STATUS_OPERATIONAL= "LAN 适配器已连接"
IDS_UNKNOW_STATUS = "未知状态"
IDS_BYTES_RECEIVED = "已接收字节数"
IDS_BYTES_SENT = "已发送字节数"
IDS_BYTES_RECEIVED_SINCE_START= "自程序启动以来已接收字节数"
IDS_BYTES_SENT_SINCE_START= "自程序启动以来已发送字节数"
IDS_PROGRAM_ELAPSED_TIME= "程序已运行时间"
IDS_HOUR_MINUTE_SECOND = "%d小时%d分%d秒"
IDS_INTERNET_IP_ADDRESS= "外网IP地址"
IDS_GET_FAILED = "获取失败"
IDS_ITEM = "项目"
IDS_VALUE = "值"
IDS_COPY_TO_CLIPBOARD_FAILED= "复制到剪贴板失败！"
IDS_SKIN_AUTHOUR = "皮肤作者："
IDS_SPEED_SHORT_MODE_TIP= "勾选后，网速显示的小数点位数将减少1位，并且单位不显示“B”"
IDS_AUTO = "自动"
IDS_FIXED_AS = "固定为"
IDS_OPEN_CONNECTION_DETIAL= "打开连接详情"
IDS_OPEN_HISTORICAL_TRAFFIC= "打开历史流量统计"
IDS_SHOW_HIDE_MORE_INFO= "显示/隐藏更多信息"
IDS_SHOW_HIDE_CPU_MEMORY= "显示/隐藏CPU和内存利用率"
IDS_OPEN_OPTION_SETTINGS= "打开选项设置"
IDS_OPEN_TASK_MANAGER = "打开任务管理器"
IDS_CHANGE_SKIN = "更换皮肤"
IDS_NONE = "无"
IDS_FONT_SIZE_WARNING = "字体大小必须在 %d~%d 之间！"
IDS_SAME_TEXT_BACK_COLOR_WARNING= "警告：文字颜色和背景色相同！"
IDS_SAME_BACK_TEXT_COLOR_WARNING= "警告：背景色和文字颜色相同！"
IDS_FOLLOWING_SYSTEM = "跟随系统"
IDS_LANGUAGE_CHANGE_INFO= "请重新启动程序使语言设置生效。"
IDS_MAIN_WINDOW_SETTINGS= "主窗口设置"
IDS_TASKBAR_WINDOW_SETTINGS= "任务栏窗口设置"
IDS_GENERAL_SETTINGS = "常规设置"
IDS_ACQUIRING = "正在获取"
IDS_LIST_VIEW = "列表视图"
IDS_CALENDAR_VIEW = "日历视图"
IDS_MONDAY = "一"
IDS_TUESDAY = "二"
IDS_WEDNESDAY = "三"
IDS_THURSDAY = "四"
IDS_FRIDAY = "五"
IDS_SATURDAY = "六"
IDS_SUNDAY = "日"
IDS_CURRENT_MONTH_TOTAL_TRAFFIC= "当前月总流量:"
IDS_TRAFFIC_USED1 = "已使用流量:"
IDS_CONNOT_INSERT_TO_TASKBAR_ERROR_LOG = "嵌入任务栏失败，已重试 <%cnt%> 次，GetLastError(): <%error_code%>。"
IDS_NO_CONNECTION = "无连接"
IDS_CONTACT_TRANSLATOR = "联系此翻译者"
IDS_THANKS_DONORS = "感谢以下捐赠者："
IDS_GET_URL_ERROR_LOG_INFO= "请求“<%1%>”时出现了错误，错误代码：<%2%>。"
IDS_SHOW_ALL_INFO_TIP = "开启此项后，右键菜单中的“选择网络连接”列表中会显示出所有的网络接口。建议仅在需要的时候开启此项。"
IDS_CFG_DIR_CHANGED_INFO = "你更改了配置文件和数据文件的保存位置，重启后生效。你可能需要手动将配置文件和数据文件转移到新的位置。"
IDS_DOUBLE_CLICK_TO_ACQUIRE= "<双击此处获取>"
IDS_ERROR1 = "错误"
IDS_ERROR_MESSAGE = "错误信息："
IDS_CRASH_INFO = "很抱歉，程序出现了错误，请重新启动程序。\r\n如果你多次遇到这个问题，请尝试到“选项”>“常规设置”中禁用硬件监控功能，并且到“插件管理”对话框中禁用所有插件。\r\n如果问题仍然存在，请将此文件 “<%1%>” 通过电子邮件发送到 <EMAIL>，以帮助作者确定问题所在。并将以下内容添加到邮件正文中："
IDS_TITLE_ACKNOWLEDGEMENT= "鸣谢"
IDS_SAVE_DEFAULT_STYLE_INQUIRY= "确实要把当前任务栏颜色设置保存到“预设<%1%>”吗？"
IDS_SPECIFIC_APP = "打开指定应用程序"
IDS_EXE_FILTER = "应用程序|*.exe|批处理文件|*.bat||"
IDS_PRESET = "预设"
IDS_LIGHT_MODE = "浅色模式"
IDS_AUTO_ADAPT_TIP_INFO= "此功能可以在Windows10深色/浅色主题改变时自动切换颜色预设方案，点击“自动适应设置”按钮以配置自动切换的预设方案。"
IDS_WITHOUT_TEMPERATURE= "Lite"
IDS_MOUSE_PENETRATE_TIP_INFO = "已开启鼠标穿透，如果需要关闭鼠标穿透，请在右下角系统通知区域找到TrafficMonitor的图标，点击鼠标右键，在菜单中关闭鼠标穿透。点击取消不再提示。"
IDS_HISTORY_TRAFFIC_LOST_ERROR_LOG = "检测到历史流量数据丢失，当前记录个数：<%1%>，已从备份文件恢复<%2%>条记录。"
IDS_LEGEND = "图例"
IDS_LICENSE_EXPLAIN = "使用本软件的源代码时请遵守以下开源协议。"
IDS_LICENSE = "开源协议"
IDS_DAY_VIEW = "日视图"
IDS_MONTH_VIEW = "月视图"
IDS_QUARTER_VIEW = "季视图"
IDS_YEAR_VIEW = "年视图"
IDS_LINEAR_SCALE = "线性比例"
IDS_LOG_SCALE = "对数比例"
IDS_CPU_TEMPERATURE = "CPU温度"
IDS_GPU_TEMPERATURE = "显卡温度"
IDS_CPU_FREQ = "CPU频率"
IDS_HDD_TEMPERATURE = "硬盘温度"
IDS_MAINBOARD_TEMPERATURE= "主板温度"
IDS_GPU_DISP = "显卡"
IDS_HDD_DISP = "硬盘"
IDS_MAINBOARD_DISP = "主板"
IDS_CPU_TEMPERATURE_EXCEED= "CPU温度已达到"
IDS_GPU_TEMPERATURE_EXCEED= "显卡温度已达到"
IDS_HDD_TEMPERATURE_EXCEED= "硬盘温度已达到"
IDS_MBD_TEMPERATURE_EXCEED= "主板温度已达到"
IDS_MUSICPLAYER2_DESCRIPTION= "MusicPlayer2: 美观易用的 Windows 本地音乐播放器"
IDS_SIMPLENOTEPAD_DESCRIPTION= "SimpleNotePad: 简洁的 Windows 文本编辑器"
IDS_COLOR = "颜色"
IDS_COLOR_LABEL = "标签颜色"
IDS_COLOR_VALUE = "数值颜色"
IDS_GPU_USAGE = "显卡利用率"
IDS_IF_OPER_STATUS_UP = "适配器已连接"
IDS_IF_OPER_STATUS_DOWN= "适配器已断开"
IDS_IF_OPER_STATUS_DORMANT= "适配器正在连接"
IDS_GOTO_GITEE = "转到此项目在Gitee上的页面"
IDS_USAGE_PERCENTAGE = "已使用百分比"
IDS_MEMORY_USED = "内存已使用"
IDS_MEMORY_AVAILABLE = "内存可用"
IDS_DOTNET_NOT_INSTALLED_TIP = "检测到系统中未安装 .Net Framework 4.5.2 或更高版本，温度监控功能将不可用。点击取消不再提示。"
IDS_VERSION_UPDATE = "版本更新"
IDS_AVREAGE_TEMPERATURE= "平均温度"
IDS_HARDWARE_MONITOR_WARNING = "警告：你正在开启硬件监控功能，硬件监控功能可以用于显示温度和显卡利用率信息。在开启硬件监控功能前请仔细阅读以下事项：\r\nTrafficMonitor不是专业的硬件监控软件，它不能保证在任何一台电脑上都能够获取到硬件信息，也无法保证获取到的硬件信息的准确性。\r\n硬件监控功能是通过第三库LibreHardwareMonitor实现的，开启硬件监控后在某些电脑中可能会出现一些问题，包括但不限于：\r\n* CPU和内存占用异常\r\n* 程序崩溃\r\n* 电脑死机\r\n请在知晓以上风险再决定开启硬件监控功能。\r\n确实要开启硬件监控功能吗？"
IDS_HDD_USAGE = "硬盘利用率"
IDS_FILE_NAME = "文件名"
IDS_STATUS = "状态"
IDS_PLUGIN_LOAD_SUCCEED= "加载成功"
IDS_PLUGIN_MODULE_LOAD_FAILED= "插件模块加载失败，故障代码：<%1%>"
IDS_PLUGIN_FUNCTION_GET_FAILED= "函数获取失败，故障代码：<%1%>"
IDS_PLUGIN_INFO = "插件详细信息"
IDS_NAME = "名称"
IDS_DESCRIPTION = "描述"
IDS_FILE_PATH = "文件路径"
IDS_ITEM_NUM = "显示项目个数"
IDS_ITEM_NAMES = "显示项目名称"
IDS_AUTHOR = "作者"
IDS_COPYRIGHT = "版权"
IDS_PLUGIN_NO_OPTIONS_INFO= "该插件没有提供选项设置。"
IDS_PLUGIN_NAME = "插件名称"
IDS_DISABLED = "已禁用"
IDS_RESTART_TO_APPLY_CHANGE_INFO= "请重新启动程序以应用此更改。"
IDS_VERSION = "版本"
IDS_DISP_ITEM_ID = "显示项目ID"
IDS_PLUGIN_API_VERSION = "接口版本"
IDS_WEEK_VIEW = "周视图"
IDS_WEEK_NUM = "第 <%1%> 周"
IDS_URL = "链接"
IDS_PLUGIN_VERSION_NOT_SUPPORT= "插件版本过低"
IDS_MODIFY_PRESET = "修改预设方案"
IDS_SELECT_AT_LEASE_ONE_WARNING= "请至少选择一项！"
IDS_AUTO_SAVE_TO_PRESET_TIP = "在开启了“自动适应Windows10深色/浅色主题”功能时，如果勾选此项，则会在任务栏窗口颜色、背景颜色等设置更改时，根据当前Windows的深色/浅色主题，自动保存到对应的预设中。"
IDS_TOTAL_NET_SPEED = "总网速"
IDS_SHOW_RESOURCE_USAGE_GRAPH_TIP= "在CPU/内存硬盘利用率、温度信息、以及插件项目上显示资源占用图"
IDS_SHOW_NET_SPEED_GRAPH_TIP= "在上传、下载和总网速上显示网速指示"
IDS_REFRESH_CONNECTION_LIST= "刷新网络连接列表"
IDS_HARDWARE_MONITOR_INIT_FAILED= "硬件监控功能初始化失败，硬件监控将无法使用！"
IDS_HARDWARE_INFO_ACQUIRE_FAILED_ERROR= "获取硬件监控数据时出现了错误！"
IDS_AUTO_RUN_METHOD_REGESTRY= "开机自动运行方式：注册表"
IDS_AUTO_RUN_METHOD_TASK_SCHEDULE= "开机自动运行方式：任务计划"
IDS_PATH = "路径"
IDS_SET_AUTO_RUN_FAILED_WARNING= "设置开机自动运行失败！"
IDS_UPDATE_TASKBARDLG_FAILED_TIP = "更新任务栏窗口内容失败，已禁用Direct2D渲染。注意：设置窗口可能不会立刻更新这一调整。"
IDS_D2DDRAWCOMMON_ERROR_TIP = "Direct2D渲染过程中出现错误，已禁用Direct2D渲染。注意：设置窗口可能不会立刻更新这一调整。"
IDS_PLUGIN_OPTIONS = "插件选项"
IDS_GET_CPU_USAGE_BY_PDH_FAILED_LOG = "使用性能计数器获取CPU利用率失败，fullCounterPath=<%1%>"
IDS_PRIMARY_DISPLAY = "主显示器"
IDS_SECONDARY_DISPLAY = "副显示器 <%1%>"
IDS_RESTORE_FROM_SLEEP_LOG = "系统已从休眠状态恢复，已重新初始化连接。（已重新初始化<%1%>次）"
IDS_PLUGIN_NEW_VERSION_INFO = "有更新，最新版本：<%1%>"
IDS_TRAFFICMONITOR_PLUGIN_NITIFICATION = "TrafficMonitor 插件通知"

TXT_OK = "确定"
TXT_CANCEL = "取消"
TXT_CLOSE = "关闭"
TXT_APPLY = "应用"

; 用于对话框中的字符串（必须以 TXT_ 开头）
; CAboutDlg
TXT_TITLE_ABOUT = "关于 TrafficMonitor"
TXT_ABOUT_VERSION = "TrafficMonitor<%1%>，<%2%> 版"
TXT_ABOUT_COPYRIGHT = "Copyright (C) 2017-2025 By ZhongYang\n最后编译日期：<compile_date>"
TXT_ABOUT_TRANSLATOR = "<%1%>翻译: <%2%>"
TXT_ABOUT_THIRD_PARTY_LIB = "本项目使用的第三方库："
TXT_ABOUT_AUTHOR_S_OTHER_SOFTWARE = "作者的其他软件："
TXT_ABOUT_CONTACT_AUTHOR = "联系作者"
TXT_ABOUT_LICENSE = "开源协议"
TXT_ABOUT_ACKNOWLEDGEMENT = "鸣谢"
TXT_ABOUT_DONATE = "捐助"

; CAppAlreadyRuningDlg
TXT_TRAFFICMONITOR_ALREAD_RUNING = "TrafficMonitor已经在运行。"
TXT_EXIT_THE_PROGRAM = "退出程序(&X)"
TXT_OPEN_OPTION_SETTINGS = "打开选项设置(&O)"
TXT_SHOW_HIDE_MAIN_WINDOW = "显示/隐藏主窗口(&M)"
TXT_SHOW_HIDE_TASKBAR_WINDOW = "显示/隐藏任务栏窗口(&T)"

; CAutoAdaptSettingsDlg
TXT_TITLE_AUTO_ADATP_SETTINGS = "自动适应设置"
TXT_COLOR_PRESET_IN_DARK_MODE = "深色Windows模式下使用的颜色预设："
TXT_COLOR_PRESET_IN_LIGHT_MODE = "浅色Windows模式下使用的颜色预设："
TXT_AUTO_SAVE_TO_PRESET_CHECK = "自动保存任务栏窗口颜色设置到预设"

; CDisplayTextSettingDlg
TXT_TITLE_DISPLAY_TEXT_SETTING = "显示文本设置"
TXT_RESTORE_DEFAULT = "全部恢复默认(&D)"

; CDonateDlg
TXT_TITLE_DONATE = "捐赠"
TXT_DONATE_INFO = "如果你觉得这个软件对你有帮助，你可以扫描下面的二维码通过支付宝或微信捐助作者，以帮助作者把这个软件做得更好。金额请随意。"

; CGeneralSettingsDlg
TXT_APPLICATION_SETTINGS = "应用程序设置"
TXT_CHECK_UPDATE = "启动时检查更新"
TXT_CHECK_NOW = "立即检查(&C)"
TXT_UPDATE_SOURCE = "更新源："
TXT_AUTO_RUN_CHECK = "开机时自动运行"
TXT_RESET_AUTO_RUN_BUTTON = "重新设置开机自动运行"
TXT_LANGUAGE = "语言:"
TXT_CONFIGURATION_AND_DATA_FILES = "配置和数据文件"
TXT_SAVE_TO_APPDATA_RADIO = "保存到 Appdata 目录"
TXT_SAVE_TO_PROGRAM_DIR_RADIO = "保存到程序所在目录"
TXT_OPEN_CONFIG_PATH_BUTTON = "打开配置文件所在目录(&D)"
TXT_NOTIFICATION_MESSAGE = "通知消息"
TXT_TODAY_TRAFFIC_TIP_CHECK = "今日已使用流量达到"
TXT_TODAY_TRAFFIC_BACK = "时通知"
TXT_MEMORY_USAGE_TIP_CHECK = "内存利用率达到"
TXT_MEMORY_USAGE_BACK = "% 时通知"
TXT_CPU_TEMP_TIP_CHECK = "CPU 温度达到"
TXT_CPU_TEMP_BACK = "°C 时通知"
TXT_GPU_TEMP_TIP_CHECK = "显卡温度达到"
TXT_GPU_TEMP_BACK = "°C 时通知"
TXT_HDD_TEMP_TIP_CHECK = "硬盘温度达到"
TXT_HDD_TEMP_BACK = "°C 时通知"
TXT_MBD_TEMP_TIP_CHECK = "主板温度达到"
TXT_MBD_TEMP_BACK = "°C 时通知"
TXT_HARDWARE_MONITORING = "硬件监控"
TXT_CPU = "CPU"
TXT_GPU = "显卡"
TXT_HARD_DISK = "硬盘"
TXT_MAIN_BOARD = "主板"
TXT_SELECT_HDD_STATIC = "选择监控的硬盘："
TXT_SELECT_CPU_STATIC = "选择监控的CPU温度："
TXT_ADVANCED = "高级"
TXT_SHOW_ALL_CONNECTION_CHECK = "显示所有网络连接"
TXT_SELECT_CONNECTIONS_BUTTON = "选择要监控的网络连接(&S)..."
TXT_CPU_ACQUISITION_METHOD = "CPU使用率获取方式："
TXT_USE_CPU_TIME_RADIO = "基于CPU使用时间"
TXT_USE_PDH_RADIO = "使用性能计数器"
TXT_USE_HARDWARE_MONITOR_RADIO = "使用硬件监控"
TXT_MONITORING_INTERVALS = "监控时间间隔："
TXT_MILLISECONDS = "毫秒"
TXT_RESTORE_DEFAULT_TIME_SPAN_BUTTON = "恢复默认(&R)"
TXT_PLUGIN_MANAGE_BUTTON = "插件管理(&P)..."
TXT_DISPLAY = "显示"
TXT_SHOW_NOTIFY_ICON_CHECK = "显示通知区图标"

; CHistoryTrafficCalendarDlg
TXT_YEAR = "年："
TXT_MONTH = "月："

; CHistoryTrafficDlg
TXT_TITLE_HISTORY_TRAFFIC = "历史流量统计"

; CHistoryTrafficListDlg
TXT_VIEW_TYPE = "统计方式："
TXT_FIGURE_VIEW_SCALE = "图表显示比例："

; CIconSelectDlg
TXT_TITLE_CHANGE_ICON = "更换通知区图标"
TXT_SELECT_A_ICON = "选择图标："
TXT_PREVIEW = "预览"
TXT_NOTIFY_ICON_AUTO_ADAPT_CHECK = "根据Windows10深浅色模式自动适应"

; CMainWndColorDlg
TXT_TITLE_MAIN_COLOR_DIALOG = "主窗口颜色设置"

; CMainWndSettingsDlg
TXT_FULLSCREEN_HIDE_CHECK = "有程序全屏运行时隐藏主窗口"
TXT_COLOR_AND_FONT = "颜色和字体"
TXT_FONT = "字体："
TXT_FONT_SIZE = "字体大小："
TXT_SET_FONT_BUTTON = "选择字体(&F)..."
TXT_TEXT_COLOR = "文本颜色："
TXT_RESOTRE_SKIN_DEFAULT_BUTTON = "恢复皮肤默认(&R)"
TXT_DISPLAY_TEXT = "显示文本"
TXT_SWITCH_UP_DOWN_CHECK = "交换上传和下载的位置"
TXT_UNIT_SETTINGS = "单位设置"
TXT_UNIT_SELECTION = "单位选择："
TXT_HIDE_UNIT_CHECK = "不显示网速单位"
TXT_SPEED_SHORT_MODE_CHECK = "网速显示简洁模式"
TXT_HIDE_PERCENTAGE_CHECK = "不显示百分号"
TXT_SPECIFY_EACH_ITEM_COLOR_CHECK = "指定每个项目的颜色"
TXT_DOUBLE_CLICK_ACTION = "双击动作:"
TXT_SEPARATE_VALUE_UNIT_CHECK = "数值和单位用空格分隔"
TXT_NETSPEED_UNIT = "网速单位："
TXT_UNIT_BYTE_RADIO = "B (字节)"
TXT_UNIT_BIT_RADIO = "b (比特)"
TXT_SHOW_TOOL_TIP_CHK = "显示鼠标提示"
TXT_EXE_PATH_STATIC = "指定应用程序："
TXT_BROWSE_BUTTON = "浏览(&B)..."
TXT_DISPLAY_TEXT_SETTING_BUTTON = "显示文本设置(&T)..."
TXT_MEMORY_DISPLAY_MODE = "内存显示方式："
TXT_ALWAYS_ON_TOP_CHECK = "总是置顶"
TXT_MOUSE_PENETRATE_CHECK = "鼠标穿透"
TXT_LOCK_WINDOW_POS_CHECK = "锁定窗口位置"
TXT_ALOW_OUT_OF_BORDER_CHECK = "允许超出屏幕边界"

; CMessageDlg
TXT_TITLE_MESSAGE_DLG = "消息"

; CNetworkInfoDlg
TXT_TITLE_NETWORK_INFO_DLG = "连接详情"

; COptionsDlg
TXT_TITLE_OPTION = "选项设置"

; CPluginManagerDlg
TXT_TITLE_PLUGIN_MANAGE = "插件管理"
TXT_OPTINS_BUTTON = "选项(&O)..."
TXT_PLUGIN_INFO_BUTTON = "详细信息(&I)..."
TXT_PLUGIN_DEV_GUID_STATIC = "插件开发指南"
TXT_PLUGIN_DOWNLOAD_STATIC = "更多插件下载"
TXT_OPEN_PLUGIN_DIR_STATIC = "打开插件目录"

;CSelectConnectionsDlg
TXT_TITLE_SELECTION_CONNECTION = "选择要监控的网络连接"

; CSetItemOrderDlg
TXT_TITLE_SELECT_ORDER_DIALOG = "显示设置"
TXT_MOVE_UP_BUTTON = "上移"
TXT_MOVE_DOWN_BUTTON = "下移"
TXT_RESTORE_DEFAULT_BUTTON = "恢复默认"

; CSkinAutoAdaptSettingDlg
TXT_TITLE_SKIN_AUTO_ADAPTT_DLG = "皮肤自动切换设置"
TXT_SKIN_IN_DARK_MODE = "深色Windows模式下使用的皮肤："
TXT_SKIN_IN_LIGHT_MODE = "浅色Windows模式下使用的皮肤："

; CSkinDlg
TXT_TITLE_SKIN_DLG = "更换皮肤"
TXT_PREVIEW_GROUP_STATIC = "预览"
TXT_SELECT_A_SKIN = "选择皮肤："
TXT_SKIN_AUTHOR = "皮肤作者"
TXT_SKIN_MAKING_UP_TUTORIAL = "皮肤制作教程"
TXT_DOWNLOAD_MORE_SKIN = "更多皮肤下载"
TXT_OPEN_SKIN_DIR = "打开皮肤目录"
TXT_SKIN_AUTO_ADAPT_CHECK = "根据Windows深浅色模式自动切换皮肤"
TXT_SKIN_AUTO_ADAPT_BUTTON = "自动切换设置..."

; CTaskbarColorDlg
TXT_TITLE_TASKBAR_COLOR_DLG = "任务栏窗口颜色设置"

; CTaskBarSettingsDlg
TXT_PRESET = "预设方案(&P)"
TXT_BACKGROUND_COLOR = "背景颜色："
TXT_BACKGROUND_TRANSPARENT = "背景透明"
TXT_AUTO_SET_BACK_COLOR = "根据任务栏颜色自动设置背景色"
TXT_AUTO_ADAPT_LIGHT_THEME = "自动适应Windows10深色/浅色主题"
TXT_AUTO_ADAPT_SETTINGS_BUTTON = "自动适应设置(&S)..."
TXT_DISPLAY_SETTINGS = "显示设置"
TXT_DISPLAY_SETTINGS_BUTTON = "显示设置(&O)..."
TXT_SPEED_SHORT_MODE = "网速显示简洁模式"
TXT_VALUE_RIGHT_ALIGN = "数值右对齐"
TXT_NET_SPEED_DATA_WIDTH = "网速数据宽度："
TXT_CHARACTORS = "字符"
TXT_HORIZONTAL_ARRANGE = "水平排列"
TXT_ITEM_SPACING = "项目间距："
TXT_VERTICAL_MARGIN = "垂直间距："
TXT_PIXELS = "像素"
TXT_TASKBAR_WINDOW = "任务栏窗口"
TXT_TASKBAR_WND_ON_LEFT = "任务栏窗口显示在任务栏的左侧"
TXT_DISPLAY_TO_SHOW_TASKBAR_WND = "显示任务栏窗口的显示器："
TXT_WIN11_SETTINGS_BUTTON = "Windows11 相关设置"
TXT_RESOURCE_USAGE_GRAPH = "资源占用图"
TXT_SHOW_RESOURCE_USAGE_GRAPH = "显示资源占用图"
TXT_SHOW_DASHED_BOX = "显示虚线框"
TXT_SHOW_NET_SPEED_GRAPH = "显示网速占用图"
TXT_NET_SPEED_GRAPH_MAX_VALUE = "网速占用图显示最大值："
TXT_USAGE_GRAPH_COLOR = "占用图颜色："
TXT_USAGE_GRAPH_FOLLOW_SYSTEM_CHECK = "跟随 Windows 主题颜色"
TXT_GRAPH_DISPLAY_MODE = "占用图类型："
TXT_BAR_MODE = "柱状图"
TXT_PLOT_MODE = "横向滚动图"
TXT_RENDERING_SETTINGS = "渲染设置"
TXT_RENDERING_SETTINGS_NOTE = "注意：只有在勾选“背景透明”且不勾选“根据任务栏颜色自动设置背景色”时才会使用Direct2D渲染"
TXT_ENABLE_COLOR_EMOJI = "使用彩色emoji"

; CWin11TaskbarSettingDlg
TXT_TITLE_WIN11_TASKBAR_SETTING = "Windows11 相关设置"
TXT_TASKBAR_WINDOWS_CLOSE_TO_ICON = "任务栏窗口靠近图标而不是靠近任务栏的两侧"
TXT_WINDOW_VERTICAL_OFFSET = "窗口垂直偏移："
TXT_WINDOW_HORIZONTAL_OFFSET = "窗口水平偏移："
TXT_AVOID_OVERLAP_RIGHT_WIDGETS_CHECK = "避免与右侧小组件重叠（如果小组件出现在任务栏的右侧，请勾选此项）"
TXT_WIDGETS_WIDTH = "小组件宽度："

[menu]
; IDR_HISTORY_TRAFFIC_MENU
TXT_SHOW_SCALE = "显示比例"
TXT_USE_LINEAR_SCALE = "使用线性比例"
TXT_USE_LOG_SCALE = "使用对数比例"
TXT_FIRST_DAT_OF_WEEEK = "一周的第一天"
TXT_SUNDAY = "星期日"
TXT_MONDAY = "星期一"
TXT_GO_TO_TODAY = "转到今天"

; IDR_INFO_MENU
TXT_COPY_TEXT = "复制文本(&C)"

; IDR_MENU1
TXT_SELECT_CONNECTIONS = "选择网络连接(&S)"
TXT_AUTO_SELECT = "自动选择(&A)"
TXT_SELECT_ALL = "选择全部(&S)"
TXT_CONNECTION_DETAILS = "连接详情(&I)"
TXT_ALWAYS_ON_TOP = "总是置顶(&T)"
TXT_MOUSE_PENETRATE = "鼠标穿透(&U)"
TXT_LOCK_WINDOW_POS = "锁定窗口位置(&L)"
TXT_SHOW_MORE_INFO = "显示更多信息(&C)"
TXT_SHOW_TASKBAR_WINDOW = "显示任务栏窗口(&W)"
TXT_SHOW_MAIN_WINDOW = "显示主窗口(&M)"
TXT_WINDOW_OPACITY = "窗口不透明度(&P)"
TXT_OTHER_FUNCTIONS = "其他功能(&E)"
TXT_CHANGE_SKIN = "更换皮肤(&S)..."
TXT_CHANGE_NOTIFY_ICON = "更换通知区图标(&N)..."
TXT_ALLOW_OUT_OF_BOUNDARIES = "允许超出屏幕边界(&B)"
TXT_HISTORY_TRAFFIC_STATISTICS = "历史流量统计(&H)"
TXT_PLUGIN_MANAGE = "插件管理(&P)..."
TXT_OPTIONS = "选项(&O)..."
TXT_HELP_MENU = "帮助(&H)"
TXT_HELP = "帮助(&H)"
TXT_FAQ = "常见问题(&F)"
TXT_UPDATE_LOG = "更新日志(&L)"
TXT_ABOUT = "关于(&A)..."
TXT_CHECK_UPDATE = "检查更新(&C)..."
TXT_EXIT = "退出(&X)"

; IDR_TASK_BAR_MENU
TXT_DISPLAY_SETTINGS = "显示设置(&P)..."
TXT_CLOSE_TASKBAR_WINDOW = "关闭任务栏窗口(&C)"
TXT_TASK_MANAGER = "任务管理器(&T)"

;IDR_PLUGIN_MANAGER_MENU
TXT_PLUGIN_DETAIL = "详细信息(&D)..."
TXT_PLUGIN_OPTIONS = "选项(&O)..."
TXT_PLUGIN_DISABLE = "禁用(&I)"

;IDR_DISPLAY_ITEM_CONTEXT_MENU
TXT_RESTORE_DEFAULT = "恢复默认(&R)"
