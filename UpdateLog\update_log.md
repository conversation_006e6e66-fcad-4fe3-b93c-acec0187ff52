**简体中文 | [繁體中文](./update_log_zh-tw.md) | [English](./update_log_en-us.md)**

# TrafficMonitor 更新日志
## V1.85.1 (2025/02/10)

* 修正Windows11下使用第三方软件改成旧版任务栏时，任务栏窗口设置中“任务栏窗口显示在任务栏左侧”选项不可用的问题
* 修正当任务栏上没有任何图标时任务栏窗口的位置不正确的问题 #1913
* 修正任务栏窗口设置中点击“应用”时网速数据宽度不生效的问题 #1916
* 任务栏设置中Windows11相关设置移动到单独的对话框中，增加避免与右侧小组件重叠的选项
* 修正未启用硬件监控中的CPU监控时，CPU使用率获取方式改成使用性能计数器后，会改回基于CPU使用时间的问题
* 修正鸣谢界面有乱码的问题
* 修正当监控时间间隔设置得过小时，显示的网速可能会大于实际网速问题 #1263 #1674
* 修正当内存超过100G时显示任务栏剩余内存显示不全的问题 #1722

## V1.85 (2025/01/21)

**功能更新：**
* 任务栏窗口增加Direct2D的渲染方式，解决有时字体边缘有锯齿的问题 #1156
* 显卡监控功能增加对英特尔显卡的支持 \#1751
* 右击插件项目时的右键菜单中增加插件菜单
* 插件管理界面增加插件图标的显示，右键菜单中增加插件命令
* 监控时间间隔的最大值调整为30s
* 任务栏窗口设置中增加“窗口顶部边距”和“垂直边距”的选项
* 主窗口增加对png格式透明背景图片的支持
* 新增根据Windows深浅色模式自动切换皮肤的功能 #1013
* 新增在任务栏的插件项目中显示资源占用图的功能，并更新对应的插件接口 #1077
* 主窗口和任务栏窗口中新增今日总流量的显示 #1174
* 任务栏窗口设置中新增“使用彩色emoji”的选项（仅Direct2D渲染方式）
* 常规设置中的温度通知上限调整为120度 #1814
* 新增Arm64EC的支持 #1703
* 新增Lite版显示CPU频率的功能
* 任务栏设置-预设方案中的方案2和方案3增加默认的文本颜色

**问题修正：**
* LibreHardwareMonitorLib.dll更新到0.9.4版本，解决部分设备硬件监控信息无法正常显示的问题
* 修正Windows 11 build 22621版本后触屏设备任务栏窗口位置不正确的问题
* 修正Windows 11下小组件不存在时，任务栏窗口的位置不正确的问题的问题 #1582
* 修正Windows 11下，当任务栏窗口显示在右侧时，会和系统小组件重叠的问题
* 修正选项设置对话框中使用触摸板手势时滚动过快的问题
* 修正主窗口鼠标提示中CPU频率显示不正确的问题
* 修正皮肤文件中插件项目的显示文本无效的问题 #1813
* 修正如果隐藏了通知区图标，弹出通知后通知区图标再次出现的问题 #1814 #1812 #1811

## V1.84.1 (2022/11/18)

* 修正Windows11下任务栏窗口无法显示到左边的问题。
* 修正任务栏显示在桌面两侧时任务栏窗口会显示不全的问题。
* 修正Windows11 22H2中，关闭了所有窗口且任务栏上没有固定任何图标时，任务栏窗口位置不正确的问题。
* 为触屏设备添加长按右键菜单的支持。
* 修正由于最新版本Windows11中搜索按钮比较宽导致任务栏窗口的位置不正确的问题。
* 修正“关于”对话框的大小有时不正确的问题。

## V1.84 (2022/10/05)

* 修正Windows11 22H2版本中任务栏窗口位置不正确的问题。
* 修正Windows11下任务栏窗口显示在左侧时会和系统自带的小组件重叠的问题。
* 新增CPU频率的显示。
* 修正主窗口网速单位缺少“/s”的问题。
* 修正写入ini文件时可能导致字符串缓冲区不足的问题。
* 插件接口更新，允许插件响应键盘事件。
* 修正任务栏窗口在不同DPI显示器切换后显示不正常的问题。
* 修正简体中文语言下历史流量统计中的“转到今天”命令无效的问题。
* 修正“插件管理”界面中在右键菜单中选择“详细信息”会弹出“选项”对话框的问题。
* 更新HardwareMonitor lib 以解决无法获取 Intel 12 代处理器温度的问题。

## V1.83 (2022/02/03)

* 修正当已经存在一个开机自启动的任务计划时，无法设置开机自启动的问题，
* 新增当已经存在TrafficMonitor进程时启动程序，弹出“程序已经在运行”对话框。
* 鼠标提示中新增插件信息的显示。
* 修正插件被禁用后仍然会被加载的问题。
* 任务栏中的插件项目支持自由排序。
* 选项设置中新增可以选择要在“网络连接列表”中显示的网络的功能。
* 新增显示总网速的功能。
* 新增在任务栏中显示网速占用图的功能。
* 修正硬件监控中有多个名称相同的硬盘时只能显示一个硬盘的问题。
* 优化任务栏窗口横向滚动图的显示效果。
* 任务栏窗口右键菜单中的“显示设置”由菜单改为对话框形式。
* “选择网络连接”菜单中添加“刷新网络连接列表”命令。
* 修正几处导致程序崩溃的问题。

## V1.82 (2021/12/12)

* 新增插件系统，可以通过插件在任务栏窗口和主窗口显示更多自定义内容。
* 任务栏右键菜单中增加“任务管理器”命令。
* 选项设置中新增“应用”按钮。
* 历史流量统计中增加周视图。
* 新增任务栏项目间距的设置。
* 修正Windows11深色模式下任务栏右键菜单无法弹出的问题。
* 修正Windows11下使用StartAllBack等软件将任务栏恢复为Windows10样式时，任务栏窗口无法显示的问题。

## V1.81 (2021/07/27)

* 修正1.80版本以来的一些崩溃的问题
* 任务栏窗口显示项目支持自定义排序
* 任务栏窗口中显示的项目数量为奇数时，最后一个项目垂直排列，以节省任务栏空间
* 新增显示硬盘利用率的功能
* 将右键菜单中的一些设置添加到选项设置界面中
* 新增主窗口对多显示器的支持，在不勾选“允许超出屏幕边界”的情况下也能移动到其他显示器了

## V1.80.3 (2021/05/29)

* 修正无法显示CPU温度的问题
* 新增可以选择监控指定CPU核心的温度的功能
* 修正一处导致程序崩溃的问题

## V1.80.2 (2021/05/22)

* 修正几处导致程序崩溃的问题
* 获取温度信息改用LibreHardwareMonitor
* 新增可选择要监控哪块硬盘的温度的功能
* 新增可选择要监控哪些硬件的功能
* 修正程序退出时LibreHardwareMonitorLib.sys文件没有解除占用的问题

## V1.80.1 (2021/05/17)

* 修正无法获取AMD显卡温度的问题
* 修正自动切换为浅色模式颜色预设时程序崩溃的问题
* 修正无法获取11代Intel处理器温度的问题
* 修正设置开机自启动无效的问题

## V1.80 (2021/05/15)

* 新增CPU、显卡、硬盘和主板温度监控的功能
* 新增显卡利用率监控的功能
* 修正历史流量统计数值溢出导致统计数值不正确的问题
* 修正屏幕分辨率更改后主窗口位置不正确的问题
* 修正系统DPI更改后，主窗口和任务栏窗口界面元素的大小不会随DPI变化的问题
* 新增任务栏窗口自由指定显示项目的功能
* 新增xml格式的主窗口皮肤，支持温度显示
* 修正使用触屏设备时，选项设置中的子窗口无法使用触屏滚动的问题
* 将检查更新的处理放到后台线程中，以解决检查更新时程序长时间没有响应的问题
* 修正任务栏窗口中按Alt+F4后程序异常的问题
* 新增对每秒4GB以上网速的支持
* 新增可选择更新源为Gitee，以解决中国大陆地区有时无法访问GitHub导致程序无法更新的问题
* 新增内存显示方式设置
* 修正当前监控的网卡被关闭或禁用，再次启动后无法自动选择之前监控的网卡的问题
* 开机自启动采用任务计划实现，以解决有时开机自启动无效的问题
* 修正一些崩溃的问题
* 其他细节方面的改进

## V1.79.1 (2020/08/05)

* 修正注册表句柄泄漏的问题。
* 修正当主窗口和任务栏窗口都不显示时，不统计CPU和内存利用率的问题。

## V1.79 (2020/07/30)

* 新增任务栏窗口颜色自动适应Windows10深色/浅色主题的功能。
* 新增通知区图标自动适应Windows10深色/浅色主题的功能。
* 增加CPU获取方式的设置，解决部分用户出现的CPU利用率始终为0的问题。
* 选项设置>任务栏设置中去掉“透明色”的设置，新增“背景色透明”的选项。
* 新增允许任务栏只显示CPU和内存利用率而不显示网速。
* 修正了关机后设置可能会没有保存的问题。
* 历史流量统计-日历视图中增加了每周第一天的设置。
* 历史流量统计-列表视图中增加了按年、月、日、季度统计的功能。
* 可能解决了历史流量数据有小概率丢失的问题。
* 修正Windows10浅色主题时，如果任务栏窗口背景设置为透明，会无法弹出右键菜单的问题。
* 修正流量统计不支持统计超过2TB数据的问题。
* 为菜单项添加了图标。
* 当程序所在目录无法写入时，将数据保存到AppData目录。
* 新增设置监控时间间隔的功能。

## V1.78 (2020/03/21)

* 新增双击主窗口或任务栏窗口打开指定应用程序的功能
* 新增任务栏窗口中显示CPU和内存利用率状态条的功能
* 修正Windows10中CPU利用率和任务管理器不一致的问题
* 新增是否显示鼠标提示的选项
* 新增程序首次启启动时根据Windows10浅色模式设置任务栏颜色的功能
* 任务栏设置中增加预设方案的功能
* 其他细节方面的改进

## V1.77 (2019/05/01)
* 增加任务窗口透明色设置，修正任务栏为白色时任务栏窗口文字颜色无法设置为黑色的问题（在“选项”——“任务栏窗口设置”中设置透明色）
* 新增程序崩溃时显示崩溃信息的功能
* 修正显示任务栏窗口的情况下，资源管理器重启时会导致屏幕闪烁的问题
* 新增鼠标指向通知区图标时显示监控信息
* 修正使用蓝牙网络时无法显示网速的问题
* 新增x64的版本
* 其他细节方面的改进
### 更新说明：
本次更新在一定程度上解决了Win10最新版本中白色任务栏时文字看不清的问题。需要手动在“选项”——“任务栏窗口设置”——“透明色”设置透明色。当透明色不为黑色且和背景色不同时为不透明效果，如果和背景色相同则为透明效果。在Win10白色任务栏中建议如下图所示设置：
![白色任务栏截图](https://user-images.githubusercontent.com/30562462/57004858-36b55300-6c05-11e9-89d8-9911dc99f09c.PNG)
## V1.76 (2018/11/11)
* 修正了分辨率更改可能会导致程序崩溃的问题；
* 新增配置文件保存位置的选项；
* 修正了当分辨率变化时任务栏窗口的垂直位置不正确的问题；
* 新增今日上传和下载流量的显示，历史流量统计中增加上传和下载流量的统计；
* 其他细节方面的改进。
## V1.75 (2018/08/11)
* 任务栏窗口右键菜单增加“选择网络连接”菜单项
* 写入配置文件时改为使用UTF8编码
* 新增网速单位为B(字节)或b(比特)的设置
* 修正部分电脑中无法获取网速的问题
* 修正部分电脑中会反复产生错误日志的问题
* 皮肤11 UI调整，修正有时文本显示不全的问题
其他细节方面的改进
## V1.74 (2018/06/17)
* 修正两处导致程序崩溃的问题
* 新增：监控所有连接的网速的功能
* 右键菜单“选择网络连接”中显示所有的网络接口，不显示loopback接口
* 新增：任务栏窗口项目可以水平排列
* 新增：繁体中文语言支持
* 其他细节方面的改进。
## V1.73 (2018/05/13)
* 新增：多语言支持，增加英语
* 新增：字体设置支持粗体、斜体、下划线等字体样式
* 新增：历史流量统计中的日历视图
* 修正：当悬浮窗在右下角时，有时程序启动后悬浮窗会往左或往上移动一段距离的问题
* 配置文件转移到C:\Users\<USER>\AppData\Roaming\TrafficMonitor目录下，防止由于程序所在目录无法写入数据导致配置文件无法保存的问题
* 新增：网速数据位数设置的选项；修正有时当网速超过10M/s时数据显示的全的问题
## V1.72 (2018/04/21)
* 新增：任务栏窗口数值右对齐的选项
* 新增：每个项目文本颜色单独设置的功能
* 新增：设置鼠标双击动作的功能
* 新增：设置当流量或内存使用率超过一定值时弹出通知的功能
* 新增：获取外网IP地址的功能
* 其他细节方面的改进。
## V1.71 (2018/04/06)
* 优化背景图片的显示效果，解决了由于图片缩放时导致的失真的问题
* 增加不规则形状皮肤的支持
* 增加可以通过皮肤文件设定项目的对齐方式和字体
* 改善了优化通知区图标在125%缩放比时的显示效果
* 一些细节方面的改进。
## V1.70 (2018/03/30)
* 新增：自定义皮肤功能，可以通过皮肤文件更加自由地定义悬浮窗中的每个项目的大小和位置
* “更换皮肤”对话框中预览图可以预览悬浮窗中显示的文本和字体，当预览图过大时会显示滚动条
* “更换皮肤”对话框中增加“皮肤制作教程”和“更多皮肤下载”链接
* 新增允许悬浮窗超出屏幕边界的选项，解决多显示器时无法移动到其他显示器的问题
## V1.69 (2018/03/17)
* 新增单位设置，允许隐藏单位
* 新增：单位设置、网速显示简洁模式可应用到主窗口
* 新增更换通知区图标的功能
* 一些细节方面的改进
* 增加两款皮肤。
## V1.68 (2018/03/03)
* 修正当程序启动时，如果设置了隐藏主窗口，主窗口还是会显示1秒钟再消失的问题
* 修正当窗口为左上角(0,0)时，下次启动时无法记住该位置的问题
* 修正有时在进入选项设置再点击“确定”时，会弹出“注册表项删除失败”提示框的问题
* 新增任务栏窗口网速显示简洁模式（选项——任务栏窗口设置——网速显示简洁模式）
## V1.67 (2018/01/21)
* 修正当主窗口和任务栏窗口都不显示CPU和内存利用率时，鼠标提示中的CPU和内存利用率不刷新的问题
* 修正了当任务栏左侧有自定义工具栏或快速启动栏时，任务栏窗口位置不正确的问题
* 新增自动检查更新功能
* 开机自启动功能改为写入注册表实现，去掉右键菜单中的“开机自动运行”项，移至选项设置——常规设置
* 增加任务栏窗口可以放在任务栏的左侧
* 其他细节方面的改进
## V1.66 (2017/12/31)
* 新增鼠标悬浮提示会实时更新
* 优化关于启动时弹出“已经有一个程序正在运行”对话框的处理
* 增加无法嵌入任务栏时自动重试的处理
* 增加任务栏窗口的刷新频率，在一定程度上解决通知区域图标变化时任务栏窗口闪烁的问题
* 历史流量统计对话框中增加图形指示
## V1.65 (2017/11.16)
* 修正了更新Win10秋季创意者更新后，设置了开机启动时会弹出“已经有一个程序正在运行”的对话框的问题
* 增加了选项设置对话框，将字体和字体颜色设置放到了选项设置对话框中，增加了显示文本的设置
* 修正了有时开机启动时会弹出“窗口无法嵌入任务栏”的提示的问题
* 增加程序全屏时隐藏悬浮窗的选项，并修正了全屏视频或游戏时悬浮窗会自己跳出来的问题
* 任务栏窗口的宽度不再固定，而是会根据文本宽度自动调整
* 连接详情对话框中增加右键菜单复制功能
## V1.64 (2017/07/28)
* 任务栏窗口信息显示改成了双缓冲绘图，彻底修正了任务栏窗口显示出现难看的色块的问题
* 当任务栏从桌面底部移动到两旁时，任务栏窗口会实时切换横排和竖排显示
## V1.63 (2017/07/05)
* 增加当无法保存设置时的警告信息
* 优化通知区图标行为，当设置了隐藏主窗口时，双击通知区图标才显示主窗口
* 调整了任务栏窗口显示网速部分的宽度，以修正网速过大时显示不全的问题
* 任务栏窗口右键菜单中增加显示通知区图标选项
* 修正有时设置了去掉通知区图标后再次启动时图标还在的问题
* 如果设置了主窗口总是置顶，每隔5分钟自动执行一次置顶操作，以解决有时窗口没有置顶的问题
## V1.62 (2017/05/29)
* 启动时如果检测到历史流量统计中有重复的日期就将它们合并
* 去掉一些菜单项中不必要的省略号
* 其他细节方面的改进
## V1.61 (2017/05/19)
* 连接详情中增加物理地址、IP地址、子网掩码和默认网关几个项目显示
* 鼠标提示信息合并为一个提示，如果设置为不显示CPU和内存利用率时在提示信息里显示
* 修正了连接详情窗口中的鼠标提示显示在窗口后面的问题
* 修正了主窗口没有置顶时任务栏窗口的鼠标提示在窗口后面的问题
* 修正了切换网络连接后的1秒网速显示为一个很大的值的问题
* 修正了当切换网络连接时统计的流量不正确的问题
## V1.60 (2017/05/11)
* 连接详情界面改为列表显示，增加程序已运行时间项目
* 新增历史流量统计功能，可以记录每一天总共使用的流量
* 关于对话框中增加捐赠功能
* 关于对话框中增加检查更新链接，点击可跳转到百度网盘链接
* 优化了自动选择的处理，在较大程度上解决了有时无法显示网速的问题
* 较大程度上解决了少数情况下设置总是置顶、鼠标穿透时无效的问题
* 新增当鼠标指向主窗口或任务栏窗口对应的项目时，提示今日已使用流量和内存使用详情
* 更换了程序图标和通知区域图标，风格更加简洁
## V1.54 (2017/04/29)
* 修正当设置了隐藏主窗口和显示任务栏窗口时，在任务视图中有一个看不见的窗口的问题
* 修正某些情况下会导致变成灰色的菜单项变回可用状态的问题
* 修正在“自动选择”模式时，当断开WIFI再重新连接后可能会出现无法自动选择有网络的连接的问题
 优化：当显示任务栏窗口时，允许隐藏通知区图标。
## V1.53 (2017/03/07)
* 修正选择皮肤时可能会出现顺序错乱的问题
* 优化：ini文件保存选择的皮肤的名称而不是序号，以解决添加或删除皮肤后选择的皮肤不正确的问题
* 修正将焦点设置到任务栏窗口后按回车或ESC键任务栏窗口关闭的问题
* 新增设置字体功能
## V1.52 (2017/03/04)
* 修正当显示任务栏窗口时，打开或关闭通知区图标会导致资源管理器卡住的问题
* 优化：打开任务栏窗口时，窗口中的信息可以立即显示出来
* 新增：允许取消开机自启动，“开机自动运行”菜单项增加复选标志
## V1.51 (2017/03/02)
* 修正重启资源管理器之后任务栏窗口无法正常显示的问题
* 修正重启资源管理器之后通知区图标消失的问题
* 修正任务栏窗口上网速显示字符串过长时字符显示不全的问题
* 修正在特定情况下可能会导致程序停止工作的问题
* 优化：当任务栏在屏幕左侧或右侧时，任务栏窗口的4个项目以竖排显示（必须关闭任务栏窗口再打开才能生效）
* “关于”对话框中增加“联系作者”超链接
## V1.50 (2017/02/25)
* 新增更换皮肤功能（点击右键菜单——其他功能——更换皮肤，新增了7套皮肤）
* 新增当网络变化时，自动更新“选择网络连接”中的子菜单项
* 新增允许交换上传和下载的位置
## V1.43 (2017/02/22)
* 优化自动选择连接的处理，不选择已断开的连接
* 修正任务栏窗口切换“显示CPU和内容利用率”后，“隐藏主窗口”的复选框消失的问题
* 优化：当任务栏窗口无法嵌入任务栏时（如被安全软件阻止），如果任务栏把窗口覆盖，则窗口自动置顶。
## V1.42 (2017/02/19)
* 优化：任务栏窗口的位置不再总是固定，当任务栏最小化区域宽度变化时自动调整任务栏窗口的位置
* 修正了当把任务放在桌面的左边或右边时任务栏窗口位置不正确的问题
## V1.41 (2017/02/18)
* 新增隐藏主界面功能
* 更改设置时实时保存，解决关机时无法保存设置的问题
* 新增开机自动运行功能，程序会创建快捷方式到开始菜单的启动目录
* 新增在无法嵌入任务栏时强行将窗口移动到正确的位置，并弹出提示信息
## V1.40 (2017/02/16)
* 增加任务栏窗口，作为工具栏样式嵌入任务栏，不再占用屏幕区域
* 增加主悬浮窗文字颜色设置
* 任务栏窗口支持背景颜色和文字颜色设置
## V1.30 (2017/02/13)
* 增加CPU和内存利用率显示。
* 增加鼠标穿透功能，悬浮窗不再影响鼠标操作
## V1.20 (2017/02/11)
* 去掉任务栏图标，增加通知区图标，右击通知区图标可弹出右键菜单
* 右键菜单增加“锁定窗口位置”、“显示通知区域图标”项目
* 增加移动时不允许超过屏幕边界的处理
* 增加程序只允许一个实例运行的处理
* 连接详情中增加自程序启动以来已发送和接收字节数显示，并增加数据以KB、MB或GB为单位的显示
* 修正了当上传速度的字符串太长时可能会导致下载速度显示不全的BUG
## V1.00 (2017/02/10)
* 初版发行
