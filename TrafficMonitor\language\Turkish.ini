﻿[general]
BCP_47 = "tr-TR"
DISPLAY_NAME = "Turkish"
TRANSLATOR = "hberkayaktas"
TRANSLATOR_URL = "https://hberkayaktas.github.io/"
DEFAULT_FONT = "Segoe UI"

[text]
; String Table
IDS_CHECK_UPDATE_FAILD = "Güncelleme denetleme başarısız oldu, lütfen ağ bağlantınızı kontrol edin!"
IDS_CHECK_UPDATE_ERROR = "Güncelleme denetleme başarısız oldu ve uzaktaki güncelleme dosyasından yanlış bilgi alındı, lütfen yazar ile iletişime geçin!"
IDS_UPDATE_AVLIABLE = "Yeni sürüm V%s tespit edildi, güncelleme yapmak istiyor musunuz?"
IDS_UPDATE_AVLIABLE2 = "Yeni sürüm V%s tespit edildi, günce<PERSON><PERSON> içeriği: \r\n%s\r\nGüncellemek istiyor musunuz?"
IDS_ALREADY_UPDATED = "Zaten en son sürüme güncellendi."
IDS_AUTORUN_FAILED_NO_KEY = "Windows başlangıcında otomatik çalıştırma yapılamıyor, kayıt defterinde ilgili anahtar bulunamadı."
IDS_AUTORUN_FAILED_NO_ACCESS = "Kayıt defteri anahtarına yazma başarısız oldu, anahtara erişim izni olmayabilir!"
IDS_AUTORUN_DELETE_FAILED = "Kayıt defteri anahtarını silme başarısız oldu, anahtara erişim izni olmayabilir!"
IDS_AN_INSTANCE_RUNNING= "Zaten çalışan bir örneği var."
IDS_TRAFFIC_USED_TODAY = "Bugün kullanılan trafik"
IDS_MEMORY_USAGE = "Bellek kullanımı"
IDS_CPU_USAGE = "CPU kullanımı"
IDS_SEND_EMAIL_TO_ATHOUR= "Yazara e-posta gönder."
IDS_GOTO_GITHUB = "Bu projenin GitHub sayfasına git."
IDS_DONATE_ATHOUR = "Yazara bağış yap"
IDS_UPLOAD = "Yükleme"
IDS_DOWNLOAD = "İndirme"
IDS_MEMORY = "Bellek"
IDS_UPLOAD_DISP = "UP"
IDS_DOWNLOAD_DISP = "DN"
IDS_MEMORY_DISP = "MEM"
IDS_CONNOT_SAVE_CONFIG_WARNING = "Uyarı: Ayarlar kaydedilemedi, ""<%file_path%>"" dosyasına veri yazılamıyor! Bu programı yönetici olarak çalıştırmak sorunu çözebilir."
IDS_TRAFFICMONITOR = "TrafficMonitor"
IDS_INSUFFICIENT_BUFFER= "Bağlantı bilgilerini depolamak için kullanılan tampon belleğin boyutu yeterli değil, bağlantı yeniden başlatıldı. (<%cnt%> kez yeniden başlatıldı)"
IDS_CONNECTION_NUM_CHANGED = "Bağlantı sayısında değişiklik tespit edildi. Bağlantı yeniden alındı. Önceki bağlantılar: <%before%>, şu anki bağlantılar: <%after%>. (<%cnt%> kez yeniden başlatıldı)"
IDS_CONNECTION_NOT_MATCH = "Bir hata meydana gelmiş olabilir, seçili bağlantı beklenen bağlantıyla uyuşmuyor. Bağlantı yeniden alındı. (<%cnt%> kez yeniden başlatıldı)"
IDS_CONNOT_INSERT_TO_TASKBAR = "Pencere görev çubuğuna başarıyla yerleştirilemedi, güvenlik yazılımı tarafından engellenmiş olabilir veya başlangıç menüsü kapatılmamış olabilir, lütfen Explorer'ı yeniden başlatmayı deneyin. TrafficMonitor denemeye devam edecek ancak bildirim yapmayacak, hata kodu:"
IDS_MEMORY_UDAGE_EXCEED= "Bellek kullanımı sınırına ulaşıldı"
IDS_NOTIFY = "Bildirim"
IDS_TODAY_TRAFFIC_EXCEED = "Bugün kullanılan trafik sınırına ulaşıldı"
IDS_DATE = "Tarih"
IDS_TRAFFIC_USED = "Toplam trafik"
IDS_FIGURE = "Grafik"
IDS_DEFAULT_ICON = "Varsayılan Simge"
IDS_ICON = "Simge"
IDS_INTERFACE_NAME = "Arayüz adı"
IDS_INTERFACE_DESCRIPTION= "Arayüz açıklaması"
IDS_CONNECTION_TYPE = "Bağlantı türü"
IDS_IF_TYPE_OTHER = "Diğer ağ türü"  
IDS_IF_TYPE_ETHERNET_CSMACD = "Ethernet ağı"  
IDS_IF_TYPE_ISO88025_TOKENRING = "Token ring ağı"  
IDS_IF_TYPE_FDDI = "Fiber Dağıtılmış Veri Arabirimi (FDDI) ağı"  
IDS_IF_TYPE_PPP = "PPP ağı"  
IDS_IF_TYPE_SOFTWARE_LOOPBACK = "Yazılımsal loopback ağı"  
IDS_IF_TYPE_ATM = "ATM ağı"  
IDS_IF_TYPE_IEEE80211 = "IEEE 802.11 kablosuz ağı"  
IDS_IF_TYPE_TUNNEL = "Tünel tipi kapsülleme ağı"  
IDS_IF_TYPE_IEEE1394 = "IEEE 1394 (Firewire) yüksek performanslı seri veri yolu ağı"  
IDS_IF_TYPE_IEEE80216_WMAN = "WiMax cihazları için mobil geniş bant arayüzü"  
IDS_IF_TYPE_WWANPP = "GSM tabanlı cihazlar için mobil geniş bant arayüzü"  
IDS_IF_TYPE_WWANPP2 = "CDMA tabanlı cihazlar için mobil geniş bant arayüzü"  
IDS_UNKNOW_CONNECTION = "Bilinmeyen ağ"  
IDS_SPEED = "Hız"  
IDS_ADAPTER_PHYSICAL_ADDRESS = "Adaptör fiziksel adresi"  
IDS_IP_ADDRESS = "IP adresi"  
IDS_SUBNET_MASK = "Alt ağ maskesi"  
IDS_DEFAULT_GATEWAY = "Varsayılan ağ geçidi"  
IDS_OPERATIONAL_STATUS = "Bağlantı durumu"  
IDS_IF_OPER_STATUS_NON_OPERATIONAL = "LAN adaptörü devre dışı bırakıldı"  
IDS_IF_OPER_STATUS_UNREACHABLE = "WAN adaptörü bağlı değil"  
IDS_IF_OPER_STATUS_DISCONNECTED = "Ağ kablosu bağlantısı kesildi veya taşıyıcı yok"  
IDS_IF_OPER_STATUS_CONNECTING = "WAN adaptörü bağlantı kuruluyor"  
IDS_IF_OPER_STATUS_CONNECTED = "WAN adaptörü uzak uç noktayla bağlantılı"  
IDS_IF_OPER_STATUS_OPERATIONAL = "LAN adaptörü bağlı"  
IDS_UNKNOW_STATUS = "Bilinmeyen durum"  
IDS_BYTES_RECEIVED = "Alınan bayt"  
IDS_BYTES_SENT = "Gönderilen bayt"  
IDS_BYTES_RECEIVED_SINCE_START = "Program başladığından beri alınan bayt"  
IDS_BYTES_SENT_SINCE_START = "Program başladığından beri gönderilen bayt"  
IDS_PROGRAM_ELAPSED_TIME = "Program çalışma süresi"  
IDS_HOUR_MINUTE_SECOND = "%d saat, %d dakika, %d saniye"  
IDS_INTERNET_IP_ADDRESS = "İnternet IP adresi"  
IDS_GET_FAILED = "Alma başarısız oldu"  
IDS_ITEM = "Öğe"  
IDS_VALUE = "Değer"  
IDS_COPY_TO_CLIPBOARD_FAILED = "Panoya kopyalama başarısız!"  
IDS_SKIN_AUTHOUR = "Tema yazarı:"  
IDS_SPEED_SHORT_MODE_TIP = "İşaretlendiğinde, ağ hız göstergesi 1 ondalık basamağa düşürülür ve birim olarak ""B"" gösterilmez."  
IDS_AUTO = "Otomatik"  
IDS_FIXED_AS = "Sabit olarak"  
IDS_OPEN_CONNECTION_DETIAL = "Bağlantı ayrıntılarını aç"  
IDS_OPEN_HISTORICAL_TRAFFIC = "Geçmiş trafik istatistiklerini aç"  
IDS_SHOW_HIDE_MORE_INFO = "Daha fazla bilgiyi göster/gizle"  
IDS_SHOW_HIDE_CPU_MEMORY = "CPU ve bellek kullanımını göster/gizle"  
IDS_OPEN_OPTION_SETTINGS = "Seçenek ayarlarını aç"  
IDS_OPEN_TASK_MANAGER = "Görev yöneticisini aç"  
IDS_CHANGE_SKIN = "Temayı değiştir"  
IDS_NONE = "Yok"  
IDS_FONT_SIZE_WARNING = "Yazı tipi boyutu %d ile %d arasında olmalıdır!"  
IDS_SAME_TEXT_BACK_COLOR_WARNING = "Uyarı: Metin rengi, arka plan rengi ile aynı!"  
IDS_SAME_BACK_TEXT_COLOR_WARNING = "Uyarı: Arka plan rengi, metin rengi ile aynı!"  
IDS_FOLLOWING_SYSTEM = "Sistemi takip et"  
IDS_LANGUAGE_CHANGE_INFO = "Dil ayarını uygulamak için lütfen uygulamayı yeniden başlatın."  
IDS_MAIN_WINDOW_SETTINGS = "Ana Pencere Ayarları"  
IDS_TASKBAR_WINDOW_SETTINGS = "Görev Çubuğu Pencere Ayarları"  
IDS_GENERAL_SETTINGS = "Genel Ayarlar"  
IDS_ACQUIRING = "Alınıyor"  
IDS_LIST_VIEW = "Liste Görünümü"  
IDS_CALENDAR_VIEW = "Takvim Görünümü"  
IDS_MONDAY = "Pzt"  
IDS_TUESDAY = "Sal"  
IDS_WEDNESDAY = "Çar"  
IDS_THURSDAY = "Per"  
IDS_FRIDAY = "Cum"  
IDS_SATURDAY = "Cmt"  
IDS_SUNDAY = "Paz"  
IDS_CURRENT_MONTH_TOTAL_TRAFFIC = "Bu ayın toplam trafiği:"  
IDS_TRAFFIC_USED1 = "Kullanılan trafik:"  
IDS_CONNOT_INSERT_TO_TASKBAR_ERROR_LOG = "Görev çubuğuna ekleme başarısız oldu, <%cnt%> kez yeniden denendi. GetLastError(): <%error_code%>."  
IDS_NO_CONNECTION = "Bağlantı Yok"  
IDS_CONTACT_TRANSLATOR = "Bu çevirmenle iletişime geçin."  
IDS_THANKS_DONORS = "Aşağıdaki bağışçılara teşekkürler:"  
IDS_GET_URL_ERROR_LOG_INFO = "İstek yapılırken bir hata oluştu= ""<%1%>"", hata kodu: <%2%>."  
IDS_SHOW_ALL_INFO_TIP = "Bu öğe işaretlendiğinde, tüm ağ arabirimleri sağ tıklama menüsündeki= ""Ağ Bağlantılarını Seç"" listesinde görüntülenecektir. Bu öğeyi yalnızca gerektiğinde işaretlemeniz önerilir."  
IDS_CFG_DIR_CHANGED_INFO = "Yapılandırma ve veri dosyalarının kaydedildiği konumu değiştirdiniz. Değişiklik, program yeniden başlatıldığında geçerli olacaktır. Yapılandırma ve veri dosyalarını yeni konuma manuel olarak aktarmanız gerekebilir."  
IDS_DOUBLE_CLICK_TO_ACQUIRE = "<Almak için buraya çift tıklayın.>"  
IDS_ERROR1 = "Hata"  
IDS_ERROR_MESSAGE = "Hata Mesajı:"  
IDS_CRASH_INFO = "Üzgünüz, program çöktü. Lütfen programı yeniden başlatın. \r\nBu sorunu birçok kez yaşarsanız, ""Seçenekler"" >= ""Genel Ayarlar"" bölümünde donanım izleme işlevini devre dışı bırakmayı ve ""Eklenti Yöneticisi"" penceresinde tüm eklentileri kapatmayı deneyin. \r\nSorun devam ederse, lütfen bu dosyayı= ""<%1%>"" adresine e-posta ile gönderin: <EMAIL>. Mesaj gövdesine aşağıdakileri ekleyin:"  
IDS_TITLE_ACKNOWLEDGEMENT = "Teşekkür"  
IDS_SAVE_DEFAULT_STYLE_INQUIRY = "Mevcut görev çubuğu renk ayarını= ""Ön Ayar <%1%>"" olarak kaydetmek istediğinizden emin misiniz?"  
IDS_SPECIFIC_APP = "Belirtilen uygulamayı aç"  
IDS_EXE_FILTER = "Uygulamalar|*.exe|Toplu iş dosyaları|*.bat||"  
IDS_PRESET = "Ön Ayar"  
IDS_LIGHT_MODE = "Açık Mod"  
IDS_AUTO_ADAPT_TIP_INFO = "Bu işlev, Windows 10 koyu/açık temaları değiştiğinde renk ön ayarlarını otomatik olarak değiştirecektir. Otomatik geçiş için ön ayar şemasını yapılandırmak için= ""Otomatik Uyum Ayarları"" düğmesine tıklayın."  
IDS_WITHOUT_TEMPERATURE = "Hafif"  
IDS_MOUSE_PENETRATE_TIP_INFO = "Fare geçişi etkinleştirildi. Fare geçişini kapatmak için sistem bildirim alanındaki TrafficMonitor simgesini bulun, sağ tıklayın ve menüden fare geçişini kapatın. Bu uyarıyı bir daha göstermemek için İptal'e tıklayın."  
IDS_HISTORY_TRAFFIC_LOST_ERROR_LOG = "Tarihsel trafik verisi kaybı tespit edildi. Mevcut kayıt sayısı: <%1%>, <%2%> kayıt yedek dosyadan geri yüklendi."  
IDS_LEGEND = "Açıklamalar"  
IDS_LICENSE_EXPLAIN = "Bu yazılımın kaynak kodunu kullanırken aşağıdaki açık kaynak protokolüne uymanız gerekmektedir."  
IDS_LICENSE = "Lisans"  
IDS_DAY_VIEW = "Günlük Görünüm"  
IDS_MONTH_VIEW = "Aylık Görünüm"  
IDS_QUARTER_VIEW = "Çeyrek Görünüm"  
IDS_YEAR_VIEW = "Yıllık Görünüm"  
IDS_LINEAR_SCALE = "Doğrusal Ölçek"  
IDS_LOG_SCALE = "Logaritmik Ölçek"  
IDS_CPU_TEMPERATURE = "CPU Sıcaklığı"  
IDS_GPU_TEMPERATURE = "GPU Sıcaklığı"  
IDS_CPU_FREQ = "CPU Frekansı"  
IDS_HDD_TEMPERATURE = "Sabit Disk Sıcaklığı"  
IDS_MAINBOARD_TEMPERATURE = "Anakart Sıcaklığı"  
IDS_GPU_DISP = "GPU"
IDS_HDD_DISP = "HDD"
IDS_MAINBOARD_DISP = "MBD"
IDS_CPU_TEMPERATURE_EXCEED = "CPU sıcaklığına ulaşıldı"  
IDS_GPU_TEMPERATURE_EXCEED = "GPU sıcaklığına ulaşıldı"  
IDS_HDD_TEMPERATURE_EXCEED = "Sabit disk sıcaklığına ulaşıldı"  
IDS_MBD_TEMPERATURE_EXCEED = "Anakart sıcaklığına ulaşıldı"  
IDS_MUSICPLAYER2_DESCRIPTION = "MusicPlayer2: Windows için güzel ve kullanımı kolay bir yerel müzik çalar"  
IDS_SIMPLENOTEPAD_DESCRIPTION = "SimpleNotePad: Windows için basit bir metin düzenleyici"  
IDS_COLOR = "Renk"  
IDS_COLOR_LABEL = "Etiket rengi"  
IDS_COLOR_VALUE = "Değer rengi"  
IDS_GPU_USAGE = "GPU kullanımı"  
IDS_IF_OPER_STATUS_UP = "Adaptör bağlı"  
IDS_IF_OPER_STATUS_DOWN = "Adaptör bağlı değil"  
IDS_IF_OPER_STATUS_DORMANT = "Adaptör bağlanıyor"  
IDS_GOTO_GITEE = "Bu proje için Gitee sayfasına git."  
IDS_USAGE_PERCENTAGE = "Kullanım yüzdesi"  
IDS_MEMORY_USED = "Kullanılan bellek"  
IDS_MEMORY_AVAILABLE = "Kullanılabilir bellek"  
IDS_DOTNET_NOT_INSTALLED_TIP = ".Net Framework v4.5.2 veya daha yüksek sürümü sistemde yüklü değil. Sıcaklık izleme fonksiyonu kullanılamayacak. Artık bu uyarıyı görmek istemiyorsanız, İptal'e tıklayın."  
IDS_VERSION_UPDATE = "Yeni sürüm güncellemesi"  
IDS_AVREAGE_TEMPERATURE = "Ortalama Sıcaklık"  
IDS_HARDWARE_MONITOR_WARNING = "Uyarı: Donanım izleme fonksiyonunu açıyorsunuz. Donanım izleme fonksiyonu, sıcaklık ve GPU kullanımı bilgilerini görüntülemek için kullanılabilir. Donanım izleme fonksiyonunu açmadan önce aşağıdakileri dikkatlice okuyun:\r\nTrafficMonitor, profesyonel bir donanım izleme yazılımı değildir. Donanım bilgilerini her bilgisayarda alabileceğini garanti edemez ve alınan donanım bilgilerinin doğruluğunu garanti edemez.\r\nDonanım izleme fonksiyonu, üçüncü taraf kütüphane LibreHardwareMonitor tarafından uygulanmaktadır. Donanım izleme açıldığında, bazı bilgisayarlarda bazı sorunlar oluşabilir, bunlar arasında ancak bunlarla sınırlı olmamak üzere:\r\n* Anormal CPU ve bellek kullanımı\r\n* Program çökmesi\r\n* Bilgisayarın çökmesi\r\nYukarıdaki riskleri fark ettikten sonra donanım izleme fonksiyonunu açmaya karar verin.\r\nDonanım izleme fonksiyonunu gerçekten açmak istiyor musunuz?"  
IDS_HDD_USAGE = "Sabit Disk kullanımı"  
IDS_FILE_NAME = "Dosya adı"  
IDS_STATUS = "Durum"  
IDS_PLUGIN_LOAD_SUCCEED = "Yükleme başarılı"  
IDS_PLUGIN_MODULE_LOAD_FAILED = "Eklenti modülü yükleme başarısız, hata kodu: <%1%>"  
IDS_PLUGIN_FUNCTION_GET_FAILED = "Fonksiyon edinme başarısız, hata kodu: <%1%>"  
IDS_PLUGIN_INFO = "Eklenti bilgileri"  
IDS_NAME = "Ad"  
IDS_DESCRIPTION = "Açıklama"  
IDS_FILE_PATH = "Dosya yolu"  
IDS_ITEM_NUM = "Gösterilen öğe sayısı"  
IDS_ITEM_NAMES = "Gösterilen öğe adları"  
IDS_AUTHOR = "Yazar"  
IDS_COPYRIGHT = "Telif hakkı"  
IDS_PLUGIN_NO_OPTIONS_INFO = "Eklenti seçenek ayarları sunmamaktadır."  
IDS_PLUGIN_NAME = "Eklenti adı"  
IDS_DISABLED = "Devre dışı"  
IDS_RESTART_TO_APPLY_CHANGE_INFO = "Bu değişikliği uygulamak için programı yeniden başlatmanız gerekiyor."  
IDS_VERSION = "Sürüm"  
IDS_DISP_ITEM_ID = "Gösterilen öğe kimliği"  
IDS_PLUGIN_API_VERSION = "API sürümü"  
IDS_WEEK_VIEW = "Haftalık görünüm"  
IDS_WEEK_NUM = "Hafta <%1%>"  
IDS_URL = "URL"  
IDS_PLUGIN_VERSION_NOT_SUPPORT = "Eklenti sürümü çok düşük."  
IDS_MODIFY_PRESET = "Ön ayarı değiştir"  
IDS_SELECT_AT_LEASE_ONE_WARNING = "Lütfen en az birini seçin!"  
IDS_AUTO_SAVE_TO_PRESET_TIP = "Windows 10 karanlık/aydınlık tema fonksiyonu etkinleştirildiğinde, bu seçenek işaretli ise, görev çubuğu pencere rengi ve arka plan rengi ayarları değiştirildiğinde, mevcut Windows karanlık/aydınlık temaya göre otomatik olarak ilgili ön ayara kaydedilecektir."  
IDS_TOTAL_NET_SPEED = "Toplam hız"  
IDS_SHOW_RESOURCE_USAGE_GRAPH_TIP = "CPU/bellek sabit disk kullanım grafikleri, sıcaklık bilgileri ve eklenti öğeleri üzerine kaynak kullanımı grafiklerini görüntüle."  
IDS_SHOW_NET_SPEED_GRAPH_TIP = "Yükleme, indirme ve toplam ağ hızı göstergelerini görüntüle."  
IDS_REFRESH_CONNECTION_LIST = "Bağlantı listesini yenile"  
IDS_HARDWARE_MONITOR_INIT_FAILED = "Donanım izleme fonksiyonu başlatılamadı, donanım izleme kullanılamayacak!"  
IDS_HARDWARE_INFO_ACQUIRE_FAILED_ERROR = "Donanım izleme verisi alındı hatası!"  
IDS_AUTO_RUN_METHOD_REGESTRY = "Otomatik başlatma modu: Kayıt defteri"  
IDS_AUTO_RUN_METHOD_TASK_SCHEDULE = "Otomatik başlatma modu: Görev planı"  
IDS_PATH = "Yol"  
IDS_SET_AUTO_RUN_FAILED_WARNING = "Başlangıçta otomatik çalıştırma ayarlanamadı!"  
IDS_UPDATE_TASKBARDLG_FAILED_TIP = "Görev çubuğu penceresinin içeriği güncellenemedi, direct2d render işlemi devre dışı bırakıldı. Not: ayar penceresi bu ayar değişikliğini hemen güncellemeyebilir."  
IDS_D2DDRAWCOMMON_ERROR_TIP = "Direct2d render işlemi sırasında bir hata oluştu ve direct2d render devre dışı bırakıldı. Not: ayar penceresi bu ayar değişikliğini hemen güncellemeyebilir."  
IDS_PLUGIN_OPTIONS = "Eklenti Seçenekleri"  
IDS_GET_CPU_USAGE_BY_PDH_FAILED_LOG = "Performans sayacından CPU kullanımı alınamadı, fullCounterPath=<%1%>"  

TXT_OK = "Tamam"  
TXT_CANCEL = "İptal"  
TXT_CLOSE = "Kapat"  
TXT_APPLY = "Uygula"  

; Text used for dialog. (Must be started with "TXT_")
; CAboutDlg
TXT_TITLE_ABOUT = "TrafficMonitor Hakkında"  
TXT_ABOUT_VERSION = "TrafficMonitor<%1%>, V<%2%>"  
TXT_ABOUT_COPYRIGHT = "Copyright (C) 2017-2025 ZhongYang'a aittir\nSon derleme tarihi: <compile_date>"  
TXT_ABOUT_TRANSLATOR = "<%1%> Çevirmen: <%2%>"  
TXT_ABOUT_THIRD_PARTY_LIB = "Bu projede kullanılan üçüncü taraf kütüphaneler:"  
TXT_ABOUT_AUTHOR_S_OTHER_SOFTWARE = "Yazarın diğer yazılımları:"  
TXT_ABOUT_CONTACT_AUTHOR = "Yazarla iletişime geç"  
TXT_ABOUT_LICENSE = "Lisans"  
TXT_ABOUT_ACKNOWLEDGEMENT = "Teşekkür"  
TXT_ABOUT_DONATE = "Bağış Yap"  

; CAppAlreadyRuningDlg
TXT_TRAFFICMONITOR_ALREAD_RUNING = "TrafficMonitor zaten çalışıyor."
TXT_EXIT_THE_PROGRAM = "Programı &kapat"
TXT_OPEN_OPTION_SETTINGS = "&Seçenek ayarlarını aç"
TXT_SHOW_HIDE_MAIN_WINDOW = "Ana pencereyi &göster/gizle"
TXT_SHOW_HIDE_TASKBAR_WINDOW = "Görev çubuğu penceresini &göster/gizle"

; CAutoAdaptSettingsDlg
TXT_TITLE_AUTO_ADATP_SETTINGS = "Otomatik uyum ayarları"
TXT_COLOR_PRESET_IN_DARK_MODE = "Koyu Windows modunda kullanılan renk ön ayarı:"
TXT_COLOR_PRESET_IN_LIGHT_MODE = "Açık Windows modunda kullanılan renk ön ayarı:"
TXT_AUTO_SAVE_TO_PRESET_CHECK = "Görev çubuğu pencere renk ayarlarını otomatik olarak ön ayara kaydet"

; CDisplayTextSettingDlg
TXT_TITLE_DISPLAY_TEXT_SETTING = "Metin Görüntüleme Ayarları"
TXT_RESTORE_DEFAULT = "&Varsayılanı Geri Yükle"

; CDonateDlg
TXT_TITLE_DONATE = "Bağış Yap"
TXT_DONATE_INFO = "Bu yazılımın size yardımcı olduğunu düşünüyorsanız, aşağıdaki QR kodunu Alipay veya WeChat Pay ile tarayarak yazara bağış yapabilir ve yazılımı daha iyi hale getirmesine yardımcı olabilirsiniz. Miktar konusunda özgürsünüz."

; CGeneralSettingsDlg
TXT_APPLICATION_SETTINGS = "Uygulama Ayarları"
TXT_CHECK_UPDATE = "Başlangıçta güncellemeleri kontrol et"
TXT_CHECK_NOW = "&Şimdi kontrol et"
TXT_UPDATE_SOURCE = "Güncelleme kaynağı:"
TXT_AUTO_RUN_CHECK = "Windows başladığında otomatik çalışsın"
TXT_RESET_AUTO_RUN_BUTTON = "Otomatik çalışmayı sıfırla"
TXT_LANGUAGE = "Dil:"
TXT_CONFIGURATION_AND_DATA_FILES = "Konfigürasyon ve veri dosyaları"
TXT_SAVE_TO_APPDATA_RADIO = "Appdata dizinine kaydet"
TXT_SAVE_TO_PROGRAM_DIR_RADIO = "Program dizinine kaydet"
TXT_OPEN_CONFIG_PATH_BUTTON = "Konfigürasyon dosyası &dizinini aç"
TXT_NOTIFICATION_MESSAGE = "Bildirim Mesajı"
TXT_TODAY_TRAFFIC_TIP_CHECK = "Bugün trafik limitine ulaşıldığında bildir"
TXT_TODAY_TRAFFIC_BACK = " "
TXT_MEMORY_USAGE_TIP_CHECK = "Bellek kullanımı limitine ulaşıldığında bildir"
TXT_MEMORY_USAGE_BACK = "%"
TXT_CPU_TEMP_TIP_CHECK = "CPU sıcaklık limiti aşıldığında bildir"
TXT_CPU_TEMP_BACK = "°C"
TXT_GPU_TEMP_TIP_CHECK = "GPU sıcaklık limiti aşıldığında bildir"
TXT_GPU_TEMP_BACK = "°C"
TXT_HDD_TEMP_TIP_CHECK = "Hard disk sıcaklık limiti aşıldığında bildir"
TXT_HDD_TEMP_BACK = "°C"
TXT_MBD_TEMP_TIP_CHECK = "Ana kart sıcaklık limiti aşıldığında bildir"
TXT_MBD_TEMP_BACK = "°C"
TXT_HARDWARE_MONITORING = "Donanım İzleme"
TXT_CPU = "CPU"
TXT_GPU = "GPU"
TXT_HARD_DISK = "Hard disk"
TXT_MAIN_BOARD = "Ana kart"
TXT_SELECT_HDD_STATIC = "İzlenecek hard diski seç:"
TXT_SELECT_CPU_STATIC = "İzlenecek CPU sıcaklığını seç:"
TXT_ADVANCED = "Gelişmiş"
TXT_SHOW_ALL_CONNECTION_CHECK = "Tüm ağ bağlantılarını göster"
TXT_SELECT_CONNECTIONS_BUTTON = "&İzlenecek bağlantıyı seç..."
TXT_CPU_ACQUISITION_METHOD = "CPU kullanımı edinme yöntemi:"
TXT_USE_CPU_TIME_RADIO = "CPU zamanına göre"
TXT_USE_PDH_RADIO = "Performans sayaçlarını kullan"
TXT_USE_HARDWARE_MONITOR_RADIO = "Donanım izleyicisini kullan"
TXT_MONITORING_INTERVALS = "İzleme aralıkları:"
TXT_MILLISECONDS = "milisaniye"
TXT_RESTORE_DEFAULT_TIME_SPAN_BUTTON = "&Varsayılanı geri yükle"
TXT_PLUGIN_MANAGE_BUTTON = "Eklenti yönet..."
TXT_DISPLAY = "Görüntü"
TXT_SHOW_NOTIFY_ICON_CHECK = "Bildirim simgesini &göster"

; CHistoryTrafficCalendarDlg
TXT_YEAR = "Yıl:"
TXT_MONTH = "Ay:"

; CHistoryTrafficDlg
TXT_TITLE_HISTORY_TRAFFIC = "Geçmiş Trafik İstatistikleri"

; CHistoryTrafficListDlg
TXT_VIEW_TYPE = "Görünüm tipi:"
TXT_FIGURE_VIEW_SCALE = "Şekil görünüm ölçeği:"

; CIconSelectDlg
TXT_TITLE_CHANGE_ICON = "Bildirim Simgesini Değiştir"
TXT_SELECT_A_ICON = "Bir simge seçin:"
TXT_PREVIEW = "Önizleme"
TXT_NOTIFY_ICON_AUTO_ADAPT_CHECK = "Windows 10 karanlık/aydınlık moduna göre otomatik uyum sağla"

; CMainWndColorDlg
TXT_TITLE_MAIN_COLOR_DIALOG = "Ana Pencere Renk Ayarları"

; CMainWndSettingsDlg
TXT_FULLSCREEN_HIDE_CHECK = "Program tam ekran çalışırken ana pencereyi gizle"
TXT_COLOR_AND_FONT = "Renk ve Yazı Tipi"
TXT_FONT = "Yazı Tipi:"
TXT_FONT_SIZE = "Yazı Tipi Boyutu:"
TXT_SET_FONT_BUTTON = "Yazı Tipini Seç..."
TXT_TEXT_COLOR = "Metin Rengi:"
TXT_DISPLAY_TEXT = "Metin Görüntüle"
TXT_SWITCH_UP_DOWN_CHECK = "Yükleme ve indirme konumlarını değiştir"
TXT_UNIT_SETTINGS = "Birim Ayarları"
TXT_UNIT_SELECTION = "Birim seçimi:"
TXT_HIDE_UNIT_CHECK = "Hız birimini göstermemek"
TXT_SPEED_SHORT_MODE_CHECK = "Ağ hızı görüntüsü için kısa mod"
TXT_HIDE_PERCENTAGE_CHECK = "Yüzdeyi göstermemek"
TXT_SPECIFY_EACH_ITEM_COLOR_CHECK = "Her öğe için renkleri belirt"
TXT_DOUBLE_CLICK_ACTION = "Çift tıklama işlemi:"
TXT_SEPARATE_VALUE_UNIT_CHECK = "Değer ve birimi boşlukla ayır"
TXT_NETSPEED_UNIT = "Ağ Hızı Birimi:"
TXT_UNIT_BYTE_RADIO = "B (Bayt)"
TXT_UNIT_BIT_RADIO = "b (bit)"
TXT_SHOW_TOOL_TIP_CHK = "Fare ipucu göster"
TXT_EXE_PATH_STATIC = "Belirtilen uygulama:"
TXT_BROWSE_BUTTON = "&Gözat..."
TXT_DISPLAY_TEXT_SETTING_BUTTON = "Metin Görüntüleme Ayarları..."
TXT_MEMORY_DISPLAY_MODE = "Bellek görüntüleme modu:"
TXT_ALWAYS_ON_TOP_CHECK = "Her zaman üstte"
TXT_MOUSE_PENETRATE_CHECK = "Fare delme"
TXT_LOCK_WINDOW_POS_CHECK = "Pencere konumunu kilitle"
TXT_ALOW_OUT_OF_BORDER_CHECK = "Ekran sınırlarının dışına çıkmaya izin ver"

; CMessageDlg
TXT_TITLE_MESSAGE_DLG = "Mesaj"

; CNetworkInfoDlg
TXT_TITLE_NETWORK_INFO_DLG = "Bağlantı Detayları"

; COptionsDlg
TXT_TITLE_OPTION = "Seçenek Ayarları"

; CPluginManagerDlg
TXT_TITLE_PLUGIN_MANAGE = "Eklenti Yönetimi"
TXT_OPTINS_BUTTON = "&Seçenekler..."
TXT_PLUGIN_INFO_BUTTON = "&Detaylar..."
TXT_PLUGIN_DEV_GUID_STATIC = "Eklenti geliştirme kılavuzu"
TXT_PLUGIN_DOWNLOAD_STATIC = "Daha fazla eklenti indir"
TXT_OPEN_PLUGIN_DIR_STATIC = "Eklenti dizinini aç"

;CSelectConnectionsDlg
TXT_TITLE_SELECTION_CONNECTION = "Bağlantıyı seçin ve izleyin"

; CSetItemOrderDlg
TXT_TITLE_SELECT_ORDER_DIALOG = "Görüntü Ayarları"
TXT_MOVE_UP_BUTTON = "Yukarı Taşı"
TXT_MOVE_DOWN_BUTTON = "Aşağı Taşı"
TXT_RESTORE_DEFAULT_BUTTON = "Varsayılanı &Geri Yükle"

; CSkinAutoAdaptSettingDlg
TXT_TITLE_SKIN_AUTO_ADAPTT_DLG = "Cilt otomatik geçiş ayarları"
TXT_SKIN_IN_DARK_MODE = "Karanlık Windows modunda kullanılan cilt:"
TXT_SKIN_IN_LIGHT_MODE = "Aydınlık Windows modunda kullanılan cilt:"

; CSkinDlg
TXT_TITLE_SKIN_DLG = "Cilt Değiştir"
TXT_PREVIEW_GROUP_STATIC = "Önizleme"
TXT_SELECT_A_SKIN = "Bir cilt seç"
TXT_SKIN_AUTHOR = "Cilt yazarı:"
TXT_SKIN_MAKING_UP_TUTORIAL = "Cilt yapma eğitimi"
TXT_DOWNLOAD_MORE_SKIN = "Daha fazla cilt indir"
TXT_OPEN_SKIN_DIR = "Cilt dizinini aç"
TXT_SKIN_AUTO_ADAPT_CHECK = "Windows karanlık / aydınlık renk modlarına göre ciltleri otomatik olarak değiştir."
TXT_SKIN_AUTO_ADAPT_BUTTON = "Otomatik geçiş ayarları..."

; CTaskbarColorDlg
TXT_TITLE_TASKBAR_COLOR_DLG = "Görev Çubuğu Penceresi Renk Ayarları"

; CTaskBarSettingsDlg
TXT_PRESET = "&Ön Ayar"
TXT_BACKGROUND_COLOR = "Arka plan rengi:"
TXT_BACKGROUND_TRANSPARENT = "Arka plan şeffaf"
TXT_AUTO_SET_BACK_COLOR = "Görev çubuğu rengine göre arka plan rengini otomatik olarak ayarla"
TXT_AUTO_ADAPT_LIGHT_THEME = "Windows 10 karanlık/aydınlık temalarına otomatik uyum sağla"
TXT_AUTO_ADAPT_SETTINGS_BUTTON = "Otomatik Uyarlama &ayarları..."
TXT_DISPLAY_SETTINGS = "Görüntü Ayarları"
TXT_DISPLAY_SETTINGS_BUTTON = "&Görüntü ayarları..."
TXT_SPEED_SHORT_MODE = "Kısa modda ağ hızı gösterimi"
TXT_VALUE_RIGHT_ALIGN = "Değerleri sağa hizala"
TXT_NET_SPEED_DATA_WIDTH = "Ağ hızı veri genişliği:"
TXT_CHARACTORS = "karakterler"
TXT_HORIZONTAL_ARRANGE = "Yatay düzen"
TXT_ITEM_SPACING = "Öğe aralığı:"
TXT_VERTICAL_MARGIN = "Dikey Margin:"
TXT_PIXELS = "piksel"
TXT_TASKBAR_WINDOW = "Görev Çubuğu Penceresi"
TXT_TASKBAR_WND_ON_LEFT = "Görev çubuğu penceresi görev çubuğunun sol tarafında görünür"
TXT_SHOW_TASKBAR_WND_IN_SECONDARY_DISPLAY = "Görev çubuğu penceresini ikincil ekranda göster (varsa)"
TXT_WIN11_SETTINGS_BUTTON = "Windows11 ile ilgili ayarlar"
TXT_RESOURCE_USAGE_GRAPH = "Kaynak kullanım grafiği"
TXT_SHOW_RESOURCE_USAGE_GRAPH = "Kaynak kullanım grafiğini göster"
TXT_SHOW_DASHED_BOX = "Kesikli kutuyu göster"
TXT_SHOW_NET_SPEED_GRAPH = "Ağ hızı grafiğini göster"
TXT_NET_SPEED_GRAPH_MAX_VALUE = "Ağ hızı grafiğinin maksimum değeri:"
TXT_USAGE_GRAPH_COLOR = "Kullanım grafiği rengi:"
TXT_GRAPH_DISPLAY_MODE = "Grafik görüntüleme modu:"
TXT_BAR_MODE = "Çubuk modu"
TXT_PLOT_MODE = "Çizim modu"
TXT_RENDERING_SETTINGS = "Render ayarları"
TXT_RENDERING_SETTINGS_NOTE = "Not: Arka plan şeffaf olarak işaretlendiğinde ve \"Görev çubuğu rengine göre arka plan rengini otomatik olarak ayarla\" işaretlenmediğinde Direct2D render kullanılır."
TXT_ENABLE_COLOR_EMOJI = "Renkli emojileri etkinleştir"

; CWin11TaskbarSettingDlg
TXT_TITLE_WIN11_TASKBAR_SETTING = "Windows11 ile ilgili ayarlar"
TXT_TASKBAR_WINDOWS_CLOSE_TO_ICON = "Görev çubuğu penceresini görev çubuğunun yanları yerine simgesine yakın kapat"
TXT_WINDOW_VERTICAL_OFFSET = "Pencere dikey ofseti:"
TXT_WINDOW_HORIZONTAL_OFFSET = "Pencere yatay ofseti:"
TXT_AVOID_OVERLAP_RIGHT_WIDGETS_CHECK = "Sağdaki widget'larla örtüşmeyi önle (Widget'lar görev çubuğunun sağ tarafında görünüyorsa bu seçeneği işaretleyin)"
TXT_WIDGETS_WIDTH = "Widget'lar genişliği:"

[menu]
; IDR_HISTORY_TRAFFIC_MENU
TXT_SHOW_SCALE = "Ölçeği Göster"
TXT_USE_LINEAR_SCALE = "Doğrusal Ölçek Kullan"
TXT_USE_LOG_SCALE = "Logaritmik Ölçek Kullan"
TXT_FIRST_DAT_OF_WEEEK = "Haftanın ilk günü"
TXT_SUNDAY = "Pazar"
TXT_MONDAY = "Pazartesi"
TXT_GO_TO_TODAY = "Bugüne Git"

; IDR_INFO_MENU
TXT_COPY_TEXT = "&Copy Text"

; IDR_MENU1
TXT_SELECT_CONNECTIONS = "&Ağ Bağlantılarını Seç"
TXT_AUTO_SELECT = "&Otomatik Seç"
TXT_SELECT_ALL = "&Hepsini Seç"
TXT_CONNECTION_DETAILS = "Bağlantı &Detayları"
TXT_ALWAYS_ON_TOP = "Her Zaman &Üstte"
TXT_MOUSE_PENETRATE = "Fare &Penetrasyonu"
TXT_LOCK_WINDOW_POS = "&Pencere Konumunu Kilitle"
TXT_SHOW_MORE_INFO = "Daha Fazla &Bilgi Göster"
TXT_SHOW_TASKBAR_WINDOW = "Görev Çubuğu &Penceresini Göster"
TXT_SHOW_MAIN_WINDOW = "Ana &Pencereyi Göster"
TXT_WINDOW_OPACITY = "Pencere Opaklığı"
TXT_OTHER_FUNCTIONS = "Diğer &Fonksiyonlar"
TXT_CHANGE_SKIN = "&Cildi Değiştir..."
TXT_CHANGE_NOTIFY_ICON = "Bildirim &Simgesini Değiştir..."
TXT_ALLOW_OUT_OF_BOUNDARIES = "Ekran &Sınırlarının Dışına Çıkmasına İzin Ver"
TXT_HISTORY_TRAFFIC_STATISTICS = "&Geçmiş Trafik İstatistikleri"
TXT_PLUGIN_MANAGE = "Eklenti Yönetimi..."
TXT_OPTIONS = "&Ayarlar..."
TXT_HELP_MENU = "&Yardım"
TXT_HELP = "&Yardım"
TXT_FAQ = "&Sıkça Sorulan Sorular"
TXT_UPDATE_LOG = "Güncelleme &Günlüğü"
TXT_ABOUT = "&Hakkında..."
TXT_CHECK_UPDATE = "&Güncellemeyi Kontrol Et..."
TXT_EXIT = "Çıkış"

; IDR_TASK_BAR_MENU
TXT_DISPLAY_SETTINGS = "Gösterim &Ayarları..."
TXT_CLOSE_TASKBAR_WINDOW = "&Görev Çubuğu Penceresini Kapat"
TXT_TASK_MANAGER = "&Görev Yöneticisi"

;IDR_PLUGIN_MANAGER_MENU
TXT_PLUGIN_DETAIL = "&Ayrıntılar..."
TXT_PLUGIN_OPTIONS = "&Seçenekler..."
TXT_PLUGIN_DISABLE = "D&isabled"
