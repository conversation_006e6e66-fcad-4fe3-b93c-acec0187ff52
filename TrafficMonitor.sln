﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.7.34031.279
MinimumVisualStudioVersion = 10.0.40219.1
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "TrafficMonitor", "TrafficMonitor\TrafficMonitor.vcxproj", "{09483BED-B1E9-4827-8120-A18302C84AA8}"
	ProjectSection(ProjectDependencies) = postProject
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26} = {C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "OpenHardwareMonitorApi", "OpenHardwareMonitorApi\OpenHardwareMonitorApi.vcxproj", "{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "PluginDemo", "PluginDemo\PluginDemo.vcxproj", "{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug (lite)|ARM64EC = Debug (lite)|ARM64EC
		Debug (lite)|x64 = Debug (lite)|x64
		Debug (lite)|x86 = Debug (lite)|x86
		Debug|ARM64EC = Debug|ARM64EC
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release (lite)|ARM64EC = Release (lite)|ARM64EC
		Release (lite)|x64 = Release (lite)|x64
		Release (lite)|x86 = Release (lite)|x86
		Release|ARM64EC = Release|ARM64EC
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Debug (lite)|ARM64EC.ActiveCfg = Debug (lite)|ARM64EC
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Debug (lite)|ARM64EC.Build.0 = Debug (lite)|ARM64EC
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Debug (lite)|x64.ActiveCfg = Debug (lite)|x64
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Debug (lite)|x64.Build.0 = Debug (lite)|x64
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Debug (lite)|x86.ActiveCfg = Debug (lite)|Win32
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Debug (lite)|x86.Build.0 = Debug (lite)|Win32
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Debug|ARM64EC.ActiveCfg = Debug|ARM64EC
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Debug|ARM64EC.Build.0 = Debug|ARM64EC
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Debug|x64.ActiveCfg = Debug|x64
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Debug|x64.Build.0 = Debug|x64
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Debug|x86.ActiveCfg = Debug|Win32
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Debug|x86.Build.0 = Debug|Win32
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Release (lite)|ARM64EC.ActiveCfg = Release (lite)|ARM64EC
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Release (lite)|ARM64EC.Build.0 = Release (lite)|ARM64EC
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Release (lite)|x64.ActiveCfg = Release (lite)|x64
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Release (lite)|x64.Build.0 = Release (lite)|x64
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Release (lite)|x86.ActiveCfg = Release (lite)|Win32
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Release (lite)|x86.Build.0 = Release (lite)|Win32
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Release|ARM64EC.ActiveCfg = Release|ARM64EC
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Release|ARM64EC.Build.0 = Release|ARM64EC
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Release|x64.ActiveCfg = Release|x64
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Release|x64.Build.0 = Release|x64
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Release|x86.ActiveCfg = Release|Win32
		{09483BED-B1E9-4827-8120-A18302C84AA8}.Release|x86.Build.0 = Release|Win32
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}.Debug (lite)|ARM64EC.ActiveCfg = Debug|x64
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}.Debug (lite)|x64.ActiveCfg = Debug|x64
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}.Debug (lite)|x86.ActiveCfg = Debug|Win32
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}.Debug|ARM64EC.ActiveCfg = Debug|x64
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}.Debug|ARM64EC.Build.0 = Debug|x64
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}.Debug|x64.ActiveCfg = Debug|x64
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}.Debug|x64.Build.0 = Debug|x64
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}.Debug|x86.ActiveCfg = Debug|Win32
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}.Debug|x86.Build.0 = Debug|Win32
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}.Release (lite)|ARM64EC.ActiveCfg = Release|x64
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}.Release (lite)|x64.ActiveCfg = Release|x64
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}.Release (lite)|x86.ActiveCfg = Release|Win32
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}.Release|ARM64EC.ActiveCfg = Release|x64
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}.Release|ARM64EC.Build.0 = Release|x64
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}.Release|x64.ActiveCfg = Release|x64
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}.Release|x64.Build.0 = Release|x64
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}.Release|x86.ActiveCfg = Release|Win32
		{C0A42F4A-ABB3-4575-B4D5-CEDD8379AC26}.Release|x86.Build.0 = Release|Win32
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Debug (lite)|ARM64EC.ActiveCfg = Debug|ARM64EC
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Debug (lite)|ARM64EC.Build.0 = Debug|ARM64EC
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Debug (lite)|x64.ActiveCfg = Debug|x64
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Debug (lite)|x64.Build.0 = Debug|x64
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Debug (lite)|x86.ActiveCfg = Debug|Win32
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Debug (lite)|x86.Build.0 = Debug|Win32
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Debug|ARM64EC.ActiveCfg = Debug|ARM64EC
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Debug|ARM64EC.Build.0 = Debug|ARM64EC
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Debug|x64.ActiveCfg = Debug|x64
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Debug|x64.Build.0 = Debug|x64
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Debug|x86.ActiveCfg = Debug|Win32
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Debug|x86.Build.0 = Debug|Win32
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Release (lite)|ARM64EC.ActiveCfg = Release|ARM64EC
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Release (lite)|ARM64EC.Build.0 = Release|ARM64EC
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Release (lite)|x64.ActiveCfg = Release|x64
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Release (lite)|x64.Build.0 = Release|x64
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Release (lite)|x86.ActiveCfg = Release|Win32
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Release (lite)|x86.Build.0 = Release|Win32
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Release|ARM64EC.ActiveCfg = Release|ARM64EC
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Release|ARM64EC.Build.0 = Release|ARM64EC
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Release|x64.ActiveCfg = Release|x64
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Release|x64.Build.0 = Release|x64
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Release|x86.ActiveCfg = Release|Win32
		{D1CA3ECC-DC32-445A-B734-C4DB08D4BA34}.Release|x86.Build.0 = Release|Win32
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {2DE8C8C3-89CE-4313-978B-DADEC5CB141A}
	EndGlobalSection
EndGlobal
