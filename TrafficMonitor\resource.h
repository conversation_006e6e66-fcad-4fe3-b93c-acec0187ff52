﻿//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ 生成的包含文件。
// 供 TrafficMonitor.rc 使用
//
#define IDD_TRAFFICMONITOR_DIALOG       102
#define IDR_MAINFRAME                   128
#define IDR_MENU1                       130
#define IDD_NETWORK_INFO_DIALOG         131
#define IDD_ABOUTBOX                    133
#define IDD_TASK_BAR_DIALOG             135
#define IDR_TASK_BAR_MENU               137
#define IDD_SKIN_DIALOG                 138
#define IDB_BITMAP1                     140
#define IDD_HISTORY_TRAFFIC_DIALOG      141
#define IDD_DONATE_DIALOG               143
#define IDB_BITMAP2                     145
#define IDB_DONATE_BITMAP               145
#define IDI_NOFITY_ICON                 146
#define IDD_OPTIONS_DIALOG              147
#define IDD_MAIN_WND_SETTINGS_DIALOG    149
#define IDD_TASKBAR_SETTINGS_DIALOG     150
#define IDR_INFO_MENU                   153
#define IDR_MENU2                       154
#define IDR_HISTORY_TRAFFIC_MENU        154
#define IDD_GENERAL_SETTINGS_DIALOG     155
#define IDB_ABOUT_BACKGROUND_HD         157
#define IDI_NOFITY_ICON2                158
#define IDI_NOFITY_ICON3                159
#define IDD_ICON_SELECT_DIALOG          160
#define IDB_NOTIFY_ICON_PREVIEW         162
#define IDD_MAIN_COLOR_DIALOG           163
#define IDD_TASKBAR_COLOR_DIALOG        165
#define IDD_HISTORY_TRAFFIC_LIST_DIALOG 168
#define IDD_HISTORY_TRAFFIC_CALENDAR_DIALOG 170
#define IDS_SEND_EMAIL_TO_TRANSLATOR    290
#define IDR_TEXT2                       290
#define IDR_TEXT                        290
#define IDD_DIALOG1                     293
#define IDD_MESSAGE_DIALOG              293
#define IDR_ACKNOWLEDGEMENT_TEXT        294
#define IDI_NOFITY_ICON4                299
#define IDB_NOTIFY_ICON_PREVIEW_LIGHT   302
#define IDB_BITMAP3                     305
#define IDB_DONATE_WECHAT               305
#define IDD_ATUO_ADAPT_SETTING_DIALOG   306
#define IDR_LICENSE                     310
#define IDI_EXIT                        311
#define IDI_HELP                        312
#define IDI_INFO                        313
#define IDI_LOCK                        314
#define IDI_SETTINGS                    315
#define IDI_STATISTICS                  316
#define IDI_CLOSE                       317
#define IDI_PIN                         318
#define IDI_SKIN                        319
#define IDI_CONNECTION                  320
#define IDI_MOUSE                       321
#define IDI_MAIN_WINDOW                 322
#define IDI_TASKBAR_WINDOW              323
#define IDI_NOTIFY                      324
#define IDI_MORE                        325
#define IDI_ITEM                        326
#define IDI_FUNCTION                    327
#define IDI_NOTIFY_ICON5                328
#define IDD_DISPLAY_TEXT_SETTINGS_DIALOG 329
#define IDR_COMPILE_TIME                329
#define IDD_SELECT_ORDER_DIALOG         330
#define IDD_PLUGIN_MANAGER_DIALOG       332
#define IDI_PLUGINS                     334
#define IDR_MENU3                       335
#define IDR_PLUGIN_MANAGER_MENU         335
#define IDI_TASK_MANAGER                336
#define IDD_APP_ALREAD_RUNING_DIALOG    337
#define IDD_SELECT_CONNECTIONS_DIALOG   339
#define IDD_TASK_BAR_DIALOG_NOREDIRECTIONBITMAP 340
#define IDD_SKIN_AUTO_ADAPT_DLG         341
#define IDI_ICON1                       343
#define IDI_PLUGIN_DISABLED             343
#define IDD_WIN11_TASKBAR_SETTING_DLG   344
#define IDR_LANGUAGE                    346
#define IDR_LANGUAGE_DEFAULT            347
#define IDR_DISPLAY_ITEM_CONTEXT_MENU   348
#define IDS_PLUGIN_DETAILS              388
#define IDC_STATIC_INFO                 1001
#define IDC_STATIC1                     1002
#define IDC_STATIC_DOWN                 1002
#define IDC_PIXELS_STATIC1              1002
#define IDC_STATIC_UP                   1003
#define IDC_STATIC2                     1003
#define IDC_STATIC_DOWN1                1003
#define IDC_STATIC_MEMORY               1004
#define IDC_STATIC3                     1004
#define IDC_STATIC_CPU                  1005
#define IDC_STATIC4                     1005
#define IDC_STATIC_UP1                  1006
#define IDC_STATIC_VERSION              1007
#define IDC_COMBO1                      1008
#define IDC_STATIC_SKIN_S               1009
#define IDC_STATIC_SKIN_L               1010
#define IDC_STATIC_TEXT2                1012
#define IDC_SKIN_INFO                   1013
#define IDC_STATIC_ABOUT                1015
#define IDC_STATIC_MAIL                 1016
#define IDC_LIST1                       1017
#define IDC_INFO_LIST                   1017
#define IDC_INFO_LIST1                  1017
#define IDC_HISTORY_INFO_LIST           1017
#define IDC_STATIC_LICENSE              1017
#define IDC_DONATE_PIC                  1018
#define IDC_FONT_NAME_EDIT              1019
#define IDC_FONT_SIZE_EDIT              1020
#define IDC_STATIC_CHECK_UPDATE         1021
#define IDC_SET_FONT_BUTTON             1021
#define IDC_STATIC_DONATE               1022
#define IDC_TEXT_COLOR_STATIC           1022
#define IDC_UPLOAD_EDIT                 1023
#define IDC_RESOTRE_SKIN_DEFAULT_BUTTON 1023
#define IDC_DOWNLOAD_EDIT               1024
#define IDC_CPU_EDIT                    1025
#define IDC_MEMORY_EDIT                 1026
#define IDC_FONT_NAME_EDIT1             1027
#define IDC_FONT_SIZE_EDIT1             1028
#define IDC_SET_FONT_BUTTON1            1029
#define IDC_TEXT_COLOR_STATIC1          1030
#define IDC_UPLOAD_EDIT1                1031
#define IDC_DOWNLOAD_EDIT1              1032
#define IDC_CPU_EDIT1                   1033
#define IDC_MEMORY_EDIT1                1034
#define IDC_TEXT_COLOR_STATIC2          1035
#define IDC_TAB1                        1035
#define IDC_SET_DEFAULT_BUTTON          1036
#define IDC_TEXT_COLOR_STATIC3          1036
#define IDC_SET_COLOR_BUTTON2           1037
#define IDC_TRANSPARENT_COLOR_STATIC    1037
#define IDC_SET_COLOR_BUTTON1           1038
#define IDC_SET_COLOR_BUTTON3           1038
#define IDC_SWITCH_UP_DOWN_CHECK        1039
#define IDC_SWITCH_UP_DOWN_CHECK1       1040
#define IDC_SET_DEFAULT_BUTTON1         1041
#define IDC_FULLSCREEN_HIDE_CHECK       1042
#define IDC_SWITCH_UP_DOWN_CHECK2       1042
#define IDC_SPEED_SHORT_MODE_CHECK      1042
#define IDC_STATIC_GITHUB               1043
#define IDC_SPEED_SHORT_MODE_CHECK2     1043
#define IDC_SET_LIGHT_MODE_BUTTON       1043
#define IDC_CHECK_UPDATE_CHECK          1044
#define IDC_STATIC_GITEE                1044
#define IDC_CHECK_NOW_BUTTON            1045
#define IDC_AUTO_RUN_CHECK              1046
#define IDC_TASKBAR_WND_ON_LEFT_CHECK   1047
#define IDC_HIDE_UNIT_CHECK             1049
#define IDC_UNIT_COMBO                  1050
#define IDC_ICON_PREVIEW                1051
#define IDC_HIDE_PERCENTAGE_CHECK       1051
#define IDC_SKIN_COURSE_STATIC          1052
#define IDC_SKIN_COURSE_STATIC2         1053
#define IDC_SKIN_DOWNLOAD_STATIC        1053
#define IDC_PREVIEW_GROUP_STATIC        1054
#define IDC_NOTIFY_STATIC               1056
#define IDC_ALLOW_SKIN_FONT_CHECK       1057
#define IDC_CHECK2                      1058
#define IDC_ALLOW_SKIN_DISP_STR_CHECK   1058
#define IDC_VALUE_RIGHT_ALIGN_CHECK     1058
#define IDC_ALWAYS_ON_TOP_CHECK         1058
#define IDC_UP_STATIC                   1059
#define IDC_DOWN_STATIC                 1060
#define IDC_UP_VALUE_STATIC             1060
#define IDC_CPU_STATIC                  1061
#define IDC_DWON_LABLE_STATIC           1061
#define IDC_MEMORY_STATIC               1062
#define IDC_DWON_VALUE_STATIC           1062
#define IDC_SPECIFY_EACH_ITEM_COLOR_CHECK 1063
#define IDC_CPU_LABLE_STATIC            1063
#define IDC_CPU_TEMP_STATIC             1063
#define IDC_CPU_VALUE_STATIC            1064
#define IDC_GPU_TEMP_STATIC             1064
#define IDC_MEMORY_LABLE_STATIC         1065
#define IDC_HDD_TEMP_STATIC             1065
#define IDC_UP_STATIC8                  1066
#define IDC_MEMORY_VALUE_STATIC         1066
#define IDC_MEMORY_STATIC2              1066
#define IDC_MBD_TEMP_STATIC             1066
#define IDC_UP_LABLE_STATIC             1067
#define IDC_DOUBLE_CLICK_COMBO          1068
#define IDC_TODAY_TRAFFIC_TIP_CHECK     1069
#define IDC_TODAY_TRAFFIC_TIP_EDIT      1070
#define IDC_MEMORY_USAGE_TIP_CHECK      1071
#define IDC_MEMORY_USAGE_TIP_EDIT       1072
#define IDC_TODAY_TRAFFIC_TIP_COMBO     1073
#define IDC_STATIC_COPYRIGHT            1074
#define IDC_LANGUAGE_COMBO              1075
#define IDC_CPU_TEMP_LABLE_STATIC       1076
#define IDC_YEAR_COMBO                  1077
#define IDC_CPU_TEMP_VALUE_STATIC       1077
#define IDC_GPU_TEMP_TIP_CHECK          1077
#define IDC_MONTH_COMBO                 1078
#define IDC_GPU_TEMP_LABLE_STATIC       1078
#define IDC_PREVIOUS_BUTTON             1079
#define IDC_GPU_TEMP_VALUE_STATIC       1079
#define IDC_NEXT_BUTTON                 1080
#define IDC_OPEN_CONFIG_PATH_BUTTON     1080
#define IDC_HDD_TEMP_LABLE_STATIC       1080
#define IDC_DIGIT_NUMBER_COMBO          1081
#define IDC_HDD_TEMP_VALUE_STATIC       1081
#define IDC_HORIZONTAL_ARRANGE_CHECK    1082
#define IDC_MAIN_BOARD_TEMP_LABLE_STATIC 1082
#define IDC_MBD_TEMP_TIP_CHECK          1082
#define IDC_PREVIEW_BUTTON              1083
#define IDC_MAIN_BOARD_TEMP_VALUE_STATIC 1083
#define IDC_INDEX_STATIC                1084
#define IDC_STATIC_ACKNOWLEDGEMENT      1085
#define IDC_TRADITIONAL_CHINESE_TRANSLATOR_STATIC 1086
#define IDC_TRANSLATOR_STATIC           1087
#define IDC_SEPARATE_VALUE_UNIT_CHECK   1089
#define IDC_SHOW_STATUS_BAR_CHECK       1090
#define IDC_UNIT_BYTE_RADIO             1091
#define IDC_UNIT_BIT_RADIO              1092
#define IDC_SHOW_ALL_CONNECTION_CHECK   1092
#define IDC_SAVE_TO_APPDATA_RADIO       1093
#define IDC_SAVE_TO_PROGRAM_DIR_RADIO   1094
#define IDC_HELP_EDIT                   1095
#define IDC_INFO_STATIC                 1096
#define IDC_SHOW_TOOL_TIP_CHK           1097
#define IDC_DEFAULT_STYLE_BUTTON        1098
#define IDC_BROWSE_BUTTON               1100
#define IDC_EXE_PATH_EDIT               1101
#define IDC_EXE_PATH_STATIC             1102
#define IDC_TEXT_STATIC                 1103
#define IDC_CM_GRAPH_BAR_RADIO          1104
#define IDC_CM_GRAPH_PLOT_RADIO         1105
#define IDC_USE_CPU_TIME_RADIO          1106
#define IDC_USE_PDH_RADIO               1107
#define IDC_BACKGROUND_TRANSPARENT_CHECK 1108
#define IDC_USE_HARDWARE_MONITOR_RADIO  1108
#define IDC_AUTO_ADAPT_LIGHT_THEME_CHECK 1109
#define IDC_AUTO_ADAPT_SETTINGS_BUTTON  1110
#define IDC_DARK_MODE_DEFAULT_STYLE_COMBO 1111
#define IDC_LIGHT_MODE_DEFAULT_STYLE_COMBO 1112
#define IDC_MENU_BUTTON                 1112
#define IDC_AUTO_ADAPT_CHECK            1113
#define IDC_MONITOR_SPAN_EDIT           1114
#define IDC_VIEW_TYPE_COMBO             1115
#define IDC_VIEW_SCALE_COMBO            1116
#define IDC_HDD_TEMP_TIP_CHECK          1117
#define IDC_AUTO_SET_BACK_COLOR_CHECK   1117
#define IDC_RESTORE_DEFAULT_BUTTON      1118
#define IDC_DISPLAY_TEXT_SETTING_BUTTON 1119
#define IDC_CPU_TEMP_TIP_CHECK          1120
#define IDC_OPENHARDWAREMONITOR_LINK    1122
#define IDC_TINYXML2_LINK               1123
#define IDC_MUSICPLAYER2_LINK           1124
#define IDC_SIMPLENOTEPAD_LINK          1125
#define IDC_GITHUB_RADIO                1126
#define IDC_GITEE_RADIO                 1127
#define IDC_MEMORY_DISPLAY_COMBO        1127
#define IDC_RESTORE_DEFAULT_TIME_SPAN_BUTTON 1128
#define IDC_SELECT_HARD_DISK_COMBO      1129
#define IDC_CPU_CHECK                   1130
#define IDC_GPU_CHECK                   1131
#define IDC_SHOW_DASHED_BOX             1131
#define IDC_HDD_CHECK                   1132
#define IDC_MBD_CHECK                   1133
#define IDC_MOUSE_PENETRATE_CHECK       1133
#define IDC_SELECT_CPU_COMBO            1134
#define IDC_LOCK_WINDOW_POS_CHECK       1134
#define IDC_ALOW_OUT_OF_BORDER_CHECK    1135
#define IDC_MOVE_UP_BUTTON              1136
#define IDC_MOVE_DOWN_BUTTON            1137
#define IDC_SET_ORDER_BUTTON            1137
#define IDC_OPTINS_BUTTON               1139
#define IDC_PLUGIN_MANAGE_BUTTON        1140
#define IDC_PLUGIN_INFO_BUTTON          1141
#define IDC_SHOW_NOTIFY_ICON_CHECK      1142
#define IDC_HDD_STATIC                  1143
#define IDC_HARDWARE_MONITOR_STATIC     1144
#define IDC_SELECT_HDD_STATIC           1145
#define IDC_SELECT_CPU_STATIC           1146
#define IDC_APPLY_BUTTON                1147
#define IDC_TASKBAR_WND_SNAP_CHECK      1148
#define IDC_ITEM_SPACE_EDIT             1149
#define IDC_EXIT_INST_BUTTON            1150
#define IDC_OPEN_SETTINGS_BUTTON        1151
#define IDC_SHOW_HIDE_MAIN_WINDOW_BUTTON 1152
#define IDC_SHOW_HIDE_TASKBAR_WINDOW_BUTTON 1153
#define IDC_PLUGIN_DEV_GUID_STATIC      1153
#define IDC_PLUGIN_DOWNLOAD_STATIC      1154
#define IDC_SELECT_CONNECTIONS_BUTTON   1155
#define IDC_AUTO_SAVE_TO_PRESET_CHECK   1156
#define IDC_SHOW_NET_SPEED_FIGURE_CHECK 1157
#define IDC_NET_SPEED_FIGURE_MAX_VALUE_EDIT 1158
#define IDC_NET_SPEED_FIGURE_MAX_VALUE_UNIT_COMBO 1159
#define IDC_OPEN_PLUGIN_DIR_STATIC      1160
#define IDC_OPEN_SKIN_DIR_STATIC        1161
#define IDC_RESET_AUTO_RUN_BUTTON       1162
#define IDC_WINDOW_OFFSET_TOP_NEGATIVE_CHECK 1166
#define IDC_WINDOW_OFFSET_TOP_EDIT      1167
#define IDC_VERTICAL_MARGIN_EDIT        1168
#define IDC_WIDTET_WIDTH_EDIT           1168
#define IDC_VERTICAL_MARGIN_NEGATIVE_CHECK 1169
#define IDC_WINDOW_OFFSET_TOP_EDIT2     1169
#define IDC_WINDOW_OFFSET_LEFT_EDIT     1169
#define IDC_DISABLE_D2D                 1170
#define IDC_GDI_RADIO                   1171
#define IDC_D2D_RADIO                   1172
#define IDC_SKIN_AUTO_ADAPT_CHECK       1173
#define IDC_SKIN_AUTO_ADAPT_BUTTON      1174
#define IDC_DARK_MODE_SKIN_COMBO        1175
#define IDC_LIGHT_MODE_SKIN_COMBO       1176
#define IDC_ENABLE_COLOR_EMOJI_CHECK    1177
#define IDC_WIN11_SETTINGS_BUTTON       1178
#define IDC_AVOID_OVERLAP_RIGHT_WIDGETS_CHECK 1179
#define IDC_CHECK1                      1181
#define IDC_TASKBAR_WND_IN_SECONDARY_DISPLAY_CHECK 1181
#define IDC_MEMORY_USAGE_BACK_STATIC    1182
#define IDC_TODAY_TRAFFIC_BACK_STATIC   1183
#define IDC_UPDATE_SORUCE_STATIC        1184
#define IDC_LANGUAGE_STATIC             1185
#define IDC_DOUBLE_CLICK_ACTION_STATIC  1186
#define IDC_MEMORY_DISPLAY_MODE_STATIC  1187
#define IDC_TXT_COLOR_STATIC            1188
#define IDC_BACK_COLOR_STATIC           1189
#define IDC_MONITOR_INTERVAL_STATIC     1190
#define IDC_MILLISECONDS_STATIC         1191
#define IDC_NET_SPEED_WIDTH_STATIC      1192
#define IDC_CHARACTOR_STATIC            1193
#define IDC_ITEM_SPACING_STATIC         1194
#define IDC_PIXELS_STATIC               1195
#define IDC_VERTICAL_MARGIN_STATIC      1196
#define TXT_MEMORY_DISPLAY_MODE         1197
#define IDC_TXT_COLOR_LABEL_STATIC      1198
#define IDC_NET_SPEED_MAX_VALUE_STATIC  1199
#define IDC_USAGE_GRAPH_COLOR_STATIC    1200
#define IDC_GRAPH_DISPLAY_MODE_STATIC   1201
#define IDC_DARK_MODE_STATIC            1202
#define IDC_LIGHT_MODE_STATIC           1203
#define IDC_FONT_STATIC                 1204
#define IDC_FONT_SIZE_STATIC            1205
#define IDC_SELECT_ICON_STATIC          1206
#define IDC_VERTICAL_OFFSET_STATIC      1207
#define IDC_HORIZONTAL_OFFSET_STATIC    1208
#define IDC_PIXEL_STATIC                1209
#define IDC_PIXEL_STATIC1               1210
#define IDC_WIDGET_WIDTH_STATIC         1211
#define IDC_PIXEL_STATIC2               1212
#define IDC_DISPLAY_TO_SHOW_TASKBAR_WND_COMBO 1213
#define IDC_DISPLAY_TO_SHOW_TASKBAR_WND_STATIC 1214
#define IDC_USAGE_GRAPH_FOLLOW_SYSTEM_CHECK 1215
#define IDC_CPU_TEMP_TIP_EDIT           1216
#define IDC_GPU_TEMP_TIP_EDIT           1217
#define IDC_HDD_TIP_EDIT                1218
#define IDC_MBD_TEMP_TIP_EDIT           1219
#define ID_32771                        32771
#define ID_NETWORK_INFO                 32772
#define ID_32773                        32773
#define ID_ALWAYS_ON_TOP                32774
#define ID_32775                        32775
#define ID_32776                        32776
#define ID_32777                        32777
#define ID_32778                        32778
#define ID_32779                        32779
#define ID_TRANS_100                    32780
#define ID_TRANSPARENCY_100             32781
#define ID_TRANSPARENCY_80              32782
#define ID_TRANSPARENCY_60              32783
#define ID_TRANSPARENCY_40              32784
#define ID_32785                        32785
#define ID_32786                        32786
#define ID_32789                        32789
#define ID_LOCK_WINDOW_POS              32790
#define ID_32791                        32791
#define ID_SHOW_NOTIFY_ICON             32792
#define ID_32793                        32793
#define ID_SHOW_CPU_MEMORY              32794
#define ID_32795                        32795
#define ID_MOUSE_PENETRATE              32796
#define ID_32797                        32797
#define ID_32798                        32798
#define ID_TEXT_COLOR                   32799
#define ID_32800                        32800
#define ID_SHOW_TASK_BAR_WND            32801
#define ID_32802                        32802
#define ID_32803                        32803
#define ID_32804                        32804
#define ID_32805                        32805
#define ID_32806                        32806
#define ID_SET_BACK_COLOR               32807
#define ID_32808                        32808
#define ID_SET_TEXT_COLOR               32809
#define ID_32810                        32810
#define ID_SHOW_CPU_MEMORY2             32811
#define ID_32812                        32812
#define ID_MINMIZE                      32813
#define ID_32814                        32814
#define ID_AUTO_RUN_WHEN_START          32815
#define ID_HIDE_MAIN_WND                32816
#define ID_32817                        32817
#define ID_32818                        32818
#define ID_CHANGE_SKIN                  32819
#define ID_32820                        32820
#define ID_SET_FONT                     32821
#define ID_32822                        32822
#define ID_SET_                         32823
#define ID_SET_FONT2                    32824
#define ID_32825                        32825
#define ID_TRAFFIC_HISTORY              32826
#define ID_32827                        32827
#define ID_32828                        32828
#define ID_OPTIONS                      32829
#define ID_32830                        32830
#define ID_OPTIONS2                     32831
#define ID_32832                        32832
#define ID_COPY_TEXT                    32833
#define ID_32834                        32834
#define ID_32835                        32835
#define ID_USE_LINEAR_SCALE             32836
#define ID_USE_LOG_SCALE                32837
#define ID_32838                        32838
#define ID_CHANGE_NOTIFY_ICON           32839
#define ID_32840                        32840
#define ID_ALOW_OUT_OF_BORDER           32841
#define ID_32842                        32842
#define ID_32843                        32843
#define ID_32844                        32844
#define ID_32845                        32845
#define ID_CHECK_UPDATE                 32846
#define ID_SHOW_MAIN_WND                32847
#define ID_DEFAULT_STYLE1               32848
#define ID_DEFAULT_STYLE_MAX            32857
#define ID_MODIFY_DEFAULT_STYLE1        32858
#define ID_MODIFY_DEFAULT_STYLE_MAX     32867
#define ID_LIGHT_MODE_STYLE             32868
#define ID_SHOW_NET_SPEED               32869
#define ID_FIRST_DAY_OF_WEEK_SUNDAY     32870
#define ID_FIRST_DAY_OF_WEEK_MONDAY     32871
#define ID_CALENDAR_JUMP_TO_TODAY       32872
#define ID_FREQUENTY_ASKED_QUESTIONS    32873
#define ID_CMD_TEST                     32874
#define ID_SHOW_UP_SPEED                32875
#define ID_SHOW_DOWN_SPEED              32876
#define ID_SHOW_CPU_USAGE               32877
#define ID_SHOW_MEMORY_USAGE            32878
#define ID_SHOW_CPU_TEMPERATURE         32879
#define ID_SHOW_GPU_TEMPERATURE         32880
#define ID_SHOW_HDD_TEMPERATURE         32881
#define ID_SHOW_MAIN_BOARD_TEMPERATURE  32882
#define ID_SHOW_GPU                     32883
#define ID_SHOW_HDD                     32884
#define ID_UPDATE_LOG                   32885
#define ID_PLUGIN_MANAGE                32886
#define ID_SHOW_PLUGIN_ITEM_START       32887
#define ID_SHOW_PLUGIN_ITEM_MAX         33140
#define ID_SELETE_CONNECTION            33141
#define ID_SELECT_ALL_CONNECTION        33142
#define ID_SELETE_CONNECTION_MAX        33398
#define ID_PLUGIN_DETAIL                33399
#define ID_PLUGIN_OPTIONS               33400
#define ID_PLUGIN_DISABLE               33401
#define ID_SHOW_TOTAL_SPEED             33402
#define ID_OPEN_TASK_MANAGER            33403
#define ID_REFRESH_CONNECTION_LIST      33404
#define ID_DISPLAY_SETTINGS             33405
#define ID_PLUGIN_DETAIL_TASKBAR        33406
#define ID_PLUGIN_OPTIONS_TASKBAR       33407
#define ID_PLUGIN_COMMAND_START         33408
#define ID_PLUGIN_COMMAND_MAX           33664
#define ID_RESTORE_DEFAULT              33665

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE        349
#define _APS_NEXT_COMMAND_VALUE         33666
#define _APS_NEXT_CONTROL_VALUE         1220
#define _APS_NEXT_SYMED_VALUE           101
#endif
#endif
