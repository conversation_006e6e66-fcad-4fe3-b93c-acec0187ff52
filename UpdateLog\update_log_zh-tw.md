**[简体中文](./update_log.md) | 繁體中文 | [English](./update_log_en-us.md)**

# TrafficMonitor 更新日誌

## V1.85.1 (2025/02/10)

* 修正Windows11下使用第三方軟體改成舊版工作列時，工作列視窗設定中“工作列視窗顯示在工作列左側”選項不可用的問題
* 修正當工作列上沒有任何圖示時工作列視窗的位置不正確的問題 #1913
* 修正工作列視窗設定中點選“應用”時網速資料寬度不生效的問題 #1916
* 工作列設定中Windows11相關設定移動到單獨的對話方塊中，增加避免與右側小元件重疊的選項
* 修正未啟用硬體監控中的CPU監控時，CPU使用率獲取方式改成使用效能計數器後，會改回基於CPU使用時間的問題
* 修正鳴謝介面有亂碼的問題
* 修正當監控時間間隔設定得過小時，顯示的網速可能會大於實際網速問題 #1263 #1674
* 修正當記憶體超過100G時顯示工作列剩餘記憶體顯示不全的問題 #1722

## V1.85 (2025/01/21)

**功能更新：**

* 工作列視窗增加Direct2D的渲染方式，解決有時字型邊緣有鋸齒的問題 #1156
* 顯示卡監控功能增加對英特爾顯示卡的支援 \#1751
* 右擊外掛條目時的右鍵選單中增加外掛選單
* 外掛管理介面增加外掛圖示的顯示，右鍵選單中增加外掛命令
* 監控時間間隔的最大值調整為30s
* 工作列視窗設定中增加“視窗頂部邊距”和“垂直邊距”的選項
* 主視窗增加對png格式透明背景圖片的支援
* 新增根據Windows深淺色模式自動切換面板的功能 #1013
* 新增在工作列的外掛條目中顯示資源佔用圖的功能，並更新對應的外掛介面 #1077
* 主視窗和工作列視窗中新增今日總流量的顯示 #1174
* 工作列視窗設定中新增“使用彩色emoji”的選項（僅Direct2D渲染方式）
* 常規設定中的溫度通知上限調整為120度 #1814
* 新增Arm64EC的支援 #1703
* 新增Lite版顯示CPU頻率的功能
* 工作列設定-預設方案中的方案2和方案3增加預設的文字顏色

**問題修正：**

* LibreHardwareMonitorLib.dll更新到0.9.4版本，解決部分裝置硬體監控資訊無法正常顯示的問題
* 修正Windows 11 build 22621版本後觸屏裝置工作列視窗位置不正確的問題
* 修正Windows 11下小工具不存在時，工作列視窗的位置不正確的問題的問題 #1582
* 修正Windows 11下，當工作列視窗顯示在右側時，會和系統小工具重疊的問題
* 修正選項設定對話方塊中使用觸控板手勢時滾動過快的問題
* 修正主視窗滑鼠提示中CPU頻率顯示不正確的問題
* 修正面板檔案中外掛專案的顯示文字無效的問題 #1813
* 修正如果隱藏了通知區圖示，彈出通知後通知區圖示再次出現的問題 #1814 #1812 #1811

## V1.84.1 (2022/11/18)

* 修正Windows11下工作列視窗無法顯示到左邊的問題。
* 修正工作列顯示在桌面兩側時工作列視窗會顯示不全的問題。
* 修正Windows11 22H2中，關閉了所有視窗且工作列上沒有固定任何圖示時，工作列視窗位置不正確的問題。
* 為觸屏裝置新增長按右鍵選單的支援。
* 修正由於最新版本Windows11中搜索按鈕比較寬導致工作列視窗的位置不正確的問題。
* 修正“關於”對話方塊的大小有時不正確的問題。

## V1.84 (2022/10/05)

* 修正Windows11 22H2版本中工作列視窗位置不正確的問題。
* 修正Windows11下工作列視窗顯示在左側時會和系統自帶的小元件重疊的問題。
* 新增CPU頻率的顯示。
* 修正主視窗網速單位缺少“/s”的問題。
* 修正寫入ini檔時可能導致字串緩衝區不足的問題。
* 外掛程式介面更新，允許外掛程式回應鍵盤事件。
* 修正工作列視窗在不同DPI顯示器切換後顯示不正常的問題。
* 修正“外掛管理”介面中在右鍵功能表中選擇“詳細資訊”會彈出“選項”對話方塊的問題。
* 更新HardwareMonitor lib 以解決無法獲取 Intel 12 代處理器溫度的問題。

## V1.82 (2021/12/12)

* 修正當已經存在一個開機自啟動的任務計劃時，無法設定開機自啟動的問題，
* 新增當已經存在TrafficMonitor程序時啟動程式，彈出“程式已經在執行”對話方塊。
* 滑鼠提示中新增外掛資訊的顯示。
* 修正外掛被禁用後仍然會被載入的問題。
* 工作列中的外掛專案支援自由排序。
* 選項設定中新增可以選擇要在“網路連線列表”中顯示的網路的功能。
* 新增顯示總網速的功能。
* 新增在工作列中顯示網速佔用圖的功能。
* 修正硬體監控中有多個名稱相同的硬碟時只能顯示一個硬碟的問題。
* 最佳化工作列視窗橫向滾動圖的顯示效果。
* 工作列視窗右鍵選單中的“顯示設定”由選單改為對話方塊形式。
* “選擇網路連線”選單中新增“重新整理網路連線列表”命令。
* 修正幾處導致程式崩潰的問題。

## V1.82 (2021/12/12)

* 新增外掛系統，可以透過外掛在工作列視窗和主視窗顯示更多自定義內容。
* 工作列右鍵選單中增加“工作管理員”命令。
* 選項設定中新增“應用”按鈕。
* 歷史流量統計中增加周檢視。
* 新增工作列專案間距的設定。
* 修正Windows11深色模式下工作列右鍵選單無法彈出的問題。
* 修正Windows11下使用StartAllBack等軟體將工作列恢復為Windows10樣式時，工作列視窗無法顯示的問題。

## V1.81 (2021/07/27)

* 修正1.80版本以來的一些崩潰的問題
* 工作列視窗顯示專案支援自定義排序
* 工作列視窗中顯示的專案數量為奇數時，最後一個專案垂直排列，以節省工作列空間
* 新增顯示硬碟利用率的功能
* 將右鍵選單中的一些設定新增到選項設定介面中
* 新增主視窗對多顯示器的支援，在不勾選“允許超出螢幕邊界”的情況下也能移動到其他顯示器了

## V1.80.3 (2021/05/29)

* 修正無法顯示CPU溫度的問題
* 新增可以選擇監控指定CPU核心的溫度的功能
* 修正一處導致程式崩潰的問題

## V1.80.2 (2021/05/22)

* 修正幾處導致程式崩潰的問題
* 獲取溫度資訊改用LibreHardwareMonitor
* 新增可選擇要監控哪塊硬碟的溫度的功能
* 新增可選擇要監控哪些硬體的功能
* 修正程式退出時LibreHardwareMonitorLib.sys檔案沒有解除佔用的問題

## V1.80.1 (2021/05/17)

* 修正無法獲取AMD顯示卡溫度的問題
* 修正自動切換為淺色模式顏色預設時程式崩潰的問題
* 修正無法獲取11代Intel處理器溫度的問題
* 修正設定開機自啟動無效的問題

## V1.80 (2021/05/15)

* 新增CPU、顯示卡、硬碟和主機板溫度監控的功能
* 新增顯示卡利用率監控的功能
* 修正歷史流量統計數值溢位導致統計數值不正確的問題
* 修正螢幕解析度更改後主視窗位置不正確的問題
* 修正系統DPI更改後，主視窗和工作列視窗介面元素的大小不會隨DPI變化的問題
* 新增工作列視窗自由指定顯示專案的功能
* 新增xml格式的主視窗面板，支援溫度顯示
* 修正使用觸屏裝置時，選項設定中的子視窗無法使用觸屏滾動的問題
* 將檢查更新的處理放到後臺執行緒中，以解決檢查更新時程式長時間沒有響應的問題
* 修正工作列視窗中按Alt+F4後程序異常的問題
* 新增對每秒4GB以上網速的支援
* 新增可選擇更新源為Gitee，以解決中國大陸地區有時無法訪問GitHub導致程式無法更新的問題
* 新增記憶體顯示方式設定
* 修正當前監控的網絡卡被關閉或禁用，再次啟動後無法自動選擇之前監控的網絡卡的問題
* 開機自啟動採用任務計劃實現，以解決有時開機自啟動無效的問題
* 修正一些崩潰的問題
* 其他細節方面的改進

## V1.79.1 (2020/08/05)

* 修正登錄檔控制碼洩漏的問題。
* 修正當主視窗和工作列視窗都不顯示時，不統計CPU和記憶體利用率的問題。

## V1.79 (2020/07/30)

* 新增工作列視窗顏色自動適應Windows10深色/淺色主題的功能。
* 新增通知區圖示自動適應Windows10深色/淺色主題的功能。
* 增加CPU獲取方式的設定，解決部分使用者出現的CPU利用率始終為0的問題。
* 選項設定>工作列設定中去掉「透明色」選項，新增「背景色透明」的選項。
* 新增允許工作列只顯示CPU和記憶體利用量而不顯示網速。
* 修正了關機後設置可能會沒有儲存的問題。
* 歷史流量統計-日曆檢視中增加了每週第一天的設定。
* 歷史流量統計-列表檢視中增加了按年、月、日、季度統計的功能。
* 可能解決了歷史流量資料有小概率丟失的問題。
* 修正Windows10淺色主題時，如果工作列視窗背景設定為透明，會無法彈出右鍵選單的問題。
* 修正流量統計不支援統計超過2TB的問題。
* 為選單項添加了圖示。
* 當程式所在目錄無法寫入時，將資料儲存到AppData目錄。
* 新增設定監視時間間隔的功能。

## V1.78 (2020/03/21)

* 新增按兩下主視窗或工作列視窗開啟指定應用程式的功能
* 新增工作列視窗中顯示CPU和RAM可用狀態條的功能
* 修正 Windows 10 中 CPU 使用量和工作管理員不一致的問題
* 新增是否顯示游標提示的選項
* 新增程式首次啟動時根據 Windows 10 淺色模式設定工作列顏色的功能
* 工作列設定中增加預設方案的功能
* 其他細節方面的改進

## V1.77 (2019/05/01)
* 新增工作列視窗透明色設定，修正工作列為白色時工作列視窗文字顏色無法設定為黑色的問題（在 [選項] —— [工作列視窗設定] 中設定透明色）
* 新增程式崩潰時顯示崩潰資訊的功能
* 修正顯示工作列視窗的情况下，檔案總管重新啟動時會導致螢幕畫面閃爍的問題
* 新增滑鼠指向通知區域圖示時顯示監控資訊
* 修正使用藍牙網路時無法顯示網速的問題
* 新增x64的版本
* 其他細節方面的改進
### 更新說明：
本次更新在一定程度上解決了Win10最新版本中白色工作列時文字看不清的問題。需要手動在“選項”——“工作列視窗設定”——“透明色”設定透明色。當透明色不為黑色且和背景色不同時為不透明效果，如果和背景色相同則為透明效果。在Win10白色工作列中建議如下圖所示設定：
![白色任务栏截图](https://user-images.githubusercontent.com/30562462/57004858-36b55300-6c05-11e9-89d8-9911dc99f09c.PNG)

## V1.76 (2018/11/11)
* 修正了解析度更改可能會導致程式崩潰的問題；
* 新增設定檔儲存位置的選項；
* 修正了當解析度更改時工作列視窗的垂直位置不正確的問題；
* 新增今日上傳和下載流量的顯示，歷史流量統計中增加上傳和下載流量的統計；
* 其他細節方面的改進。

